{"cells": [{"cell_type": "markdown", "id": "9f31be03", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "e7e75559", "metadata": {}, "source": ["# 订阅账户"]}, {"cell_type": "code", "execution_count": null, "id": "1802ab50", "metadata": {"ExecuteTime": {"end_time": "2025-08-29T09:28:42.063727Z", "start_time": "2025-08-29T09:28:41.323366Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[AccountBalance { total_cash: 28520.74, max_finance_amount: 0.00, remaining_finance_amount: 0.00, risk_level: 0, margin_call: 0, currency: \"HKD\", cash_infos: [CashInfo { withdraw_cash: 290.02, available_cash: 1342.04, frozen_cash: 2324.10, settling_cash: 1058.20, currency: \"USD\" }, CashInfo { withdraw_cash: 0.00, available_cash: 0.00, frozen_cash: 0.00, settling_cash: 0.00, currency: \"HKD\" }], net_assets: 20295.60, init_margin: 9870.17, maintenance_margin: 9047.65, buy_power: 10377.35, frozen_transaction_fees: [FrozenTransactionFee { currency: \"USD\", frozen_transaction_fee: 6.18 }] }]\n", "StockPositionsResponse { channels: [StockPositionChannel { account_channel: \"hk_beta\", positions: [StockPosition { symbol: \"SQQQ.US\", symbol_name: \"Proshares UltraPro Short QQQ ETF\", quantity: -60, available_quantity: -60, currency: \"USD\", cost_price: 17.637, market: US, init_quantity: Some(-40) }] }] }\n"]}], "source": ["from longport.openapi import TradeContext, Config\n", "\n", "# 直接传入配置参数\n", "config = Config(\n", "    app_key=\"\",\n", "    app_secret=\"\",\n", "    access_token=\"\"\n", ")\n", "\n", "ctx = TradeContext(config)\n", "\n", "# 查询资金余额\n", "resp = ctx.account_balance()\n", "print(resp)\n", "\n", "# 查询持仓\n", "positions = ctx.stock_positions()\n", "print(positions)\n"]}, {"cell_type": "markdown", "id": "06a866bd", "metadata": {}, "source": ["# 查看今日订单"]}, {"cell_type": "code", "execution_count": 2, "id": "afaef63d", "metadata": {"ExecuteTime": {"end_time": "2025-08-29T09:28:52.277876Z", "start_time": "2025-08-29T09:28:51.783575Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Order { order_id: \"1145919054603653120\", status: Canceled, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T03:13:12Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T03:18:17Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1145920429156765696\", status: Canceled, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T03:18:40Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T03:18:47Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1145985364284968960\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T07:36:42Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T08:00:37Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1145991451339943936\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T08:00:53Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T08:08:15Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1145996414443069440\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T08:20:36Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T08:29:33Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1145999394827440128\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T08:32:27Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T08:36:40Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1146000578103513088\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T08:37:09Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T09:00:51Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1146006932687982592\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T09:02:24Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T09:12:21Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1146009628291637248\", status: Rejected, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 0, price: Some(17.04), executed_price: None, submitted_at: \"2025-08-29T09:13:07Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T09:18:05Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }, Order { order_id: \"1146011271485755392\", status: Filled, stock_name: \"Proshares UltraPro Short QQQ ETF\", quantity: 20, executed_quantity: 20, price: Some(17.04), executed_price: Some(17.57), submitted_at: \"2025-08-29T09:19:39Z\", side: Sell, symbol: \"SQQQ.US\", order_type: LO, last_done: None, trigger_price: None, msg: \"\", tag: Normal, time_in_force: Day, expire_date: Some(\"2025-August-29\"), updated_at: Some(\"2025-08-29T09:19:39Z\"), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, trigger_status: None, currency: \"USD\", outside_rth: Some(AnyTime), remark: \"\" }]\n"]}], "source": ["from longport.openapi import TradeContext\n", "ctx = TradeContext(config)\n", "\n", "resp = ctx.today_orders()\n", "print(resp)"]}, {"cell_type": "markdown", "id": "f412127e", "metadata": {}, "source": ["# 下单"]}, {"cell_type": "code", "execution_count": 6, "id": "2c2cf39b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SubmitOrderResponse { order_id: \"1143096463140630528\" }\n"]}], "source": ["from decimal import Decimal\n", "from longport.openapi import TradeContext, Config, OrderType, OrderSide, TimeInForceType\n", "\n", "ctx = TradeContext(config)\n", "\n", "# Submit order\n", "resp = ctx.submit_order(symbol=\"SQQQ.US\", \n", "                        order_type=OrderType.LO, \n", "                        side=OrderSide.Sell, \n", "                        submitted_quantity=Decimal(1), \n", "                        time_in_force=TimeInForceType.Day, \n", "                        submitted_price=Decimal(50), \n", "                        remark=\"Hello from Python SDK\")\n", "print(resp)"]}, {"cell_type": "code", "execution_count": 15, "id": "2db1a139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SubmitOrderResponse { order_id: \"1143079023283322880\" }\n"]}], "source": ["from decimal import Decimal\n", "from longport.openapi import TradeContext, Config, OrderType, OrderSide, TimeInForceType\n", "\n", "ctx = TradeContext(config)\n", "\n", "# Submit order\n", "resp = ctx.submit_order(symbol=\"AAPL.US\", \n", "                        order_type=OrderType.LO, \n", "                        side=OrderSide.Buy, \n", "                        submitted_quantity=Decimal(1), \n", "                        time_in_force=TimeInForceType.Day, \n", "                        submitted_price=Decimal(10), \n", "                        remark=\"Hello from Python SDK\")\n", "print(resp)"]}, {"cell_type": "markdown", "id": "ea5c95f0", "metadata": {}, "source": ["# 设置订单回调"]}, {"cell_type": "code", "execution_count": null, "id": "7d2e3672", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SubmitOrderResponse { order_id: \"1143081007746908160\" }\n", "PushOrderChanged { side: Sell, stock_name: \"3 倍做空纳指 ETF - ProShares\", submitted_quantity: 1, symbol: \"SQQQ.US\", order_type: LO, submitted_price: 50.0000, executed_quantity: 0, executed_price: None, order_id: \"1143081007746908160\", currency: \"USD\", status: NotReported, submitted_at: \"2025-08-21T07:15:49Z\", updated_at: \"2025-08-21T07:15:49Z\", trigger_price: None, msg: \"\", tag: Normal, trigger_status: Some(Unknown), trigger_at: None, trailing_amount: None, trailing_percent: None, limit_offset: None, account_no: \"********\", last_share: None, last_price: None, remark: \"Hello from Python SDK\" }\n"]}], "source": ["from time import sleep\n", "from decimal import Decimal\n", "from longport.openapi import TradeContext, Config, OrderSide, OrderType, TimeInForceType, PushOrderChanged, TopicType\n", "\n", "# 定义订单状态变化时的回调函数\n", "def on_order_changed(event: <PERSON>ushOrderChanged):\n", "    print(event)\n", "\n", "ctx = TradeContext(config)\n", "\n", "# 注册订单变化回调\n", "ctx.set_on_order_changed(on_order_changed)\n", "\n", "# 订阅私有频道（Private = 订单、成交等推送）\n", "ctx.subscribe([TopicType.Private])\n", "\n", "# 提交一个限价单\n", "resp = ctx.submit_order(\n", "    side=OrderSide.Sell,\n", "    symbol=\"SQQQ.US\",                \n", "    order_type=OrderType.LO,        \n", "    submitted_price=Decimal(50),    \n", "    submitted_quantity=Decimal(1),\n", "    time_in_force=TimeInForceType.Day, \n", "    remark=\"Hello from Python SDK\", \n", ")\n", "print(resp)  # 打印下单结果\n", "\n", "sleep(100)  \n", "\n", "# 取消订阅\n", "ctx.unsubscribe([TopicType.Private])\n"]}, {"cell_type": "markdown", "id": "060dae0d", "metadata": {}, "source": ["# 撤单"]}, {"cell_type": "code", "execution_count": null, "id": "8f338fca", "metadata": {}, "outputs": [], "source": ["from longport.openapi import TradeContext, Config\n", "\n", "ctx = TradeContext(config)\n", "\n", "ctx.cancel_order(\"709043056541253632\")"]}, {"cell_type": "markdown", "id": "05581941", "metadata": {}, "source": ["# 改单"]}, {"cell_type": "code", "execution_count": null, "id": "8b57fcbd", "metadata": {}, "outputs": [], "source": ["from decimal import Decimal\n", "from longport.openapi import TradeContext, Config\n", "\n", "ctx = TradeContext(config)\n", "\n", "ctx.replace_order(\n", "    order_id = \"709043056541253632\",\n", "    quantity = Decimal(100),\n", "    price = Decimal(50),\n", ")"]}, {"cell_type": "markdown", "id": "4a10e386", "metadata": {}, "source": ["# Trader.py"]}, {"cell_type": "code", "execution_count": null, "id": "d2ed8221", "metadata": {}, "outputs": [], "source": ["from longport.openapi import TradeContext, Config, OrderType, OrderSide, TimeInForceType, PushOrderChanged, TopicType\n", "from decimal import Decimal\n", "import logging as lg\n", "import pandas as pd\n", "from typing import Callable, Optional\n", "\n", "class OrderstatusHandler:\n", "    \"\"\"订单状态变化处理器\"\"\"\n", "    def __init__(self, callback: Callable):\n", "        self.callback = callback\n", "\n", "    def handle_order_changed(self, event: PushOrderChanged):\n", "        \"\"\"处理订单状态变化推送\"\"\"\n", "        try:\n", "            order_status_info = {\n", "                'order_id': event.order_id,\n", "                'code': event.symbol,\n", "                'order_status': str(event.status),\n", "                'order_type': str(event.order_type)\n", "            }\n", "\n", "            lg.info(f\"订单状态更新：{order_status_info}\")\n", "            if self.callback:\n", "                self.callback(order_status_info)\n", "        \n", "        except Exception as e:\n", "            lg.error(f\"订单状态回调函数执行异常：{e}\")\n", "\n", "class Trader:\n", "    \"\"\"长桥交易接口\"\"\"\n", "    def __init__(self, app_key: str=\"9ec9c8e60d8a925feca2012fdfc46668\",\n", "                 app_secret: str=\"3c6b4fb29ed0c32d8ed1f41f2fd1432c7dd5db7cb3a2916e782fa67ca82d42c8\",\n", "                 access_token: str=\"m_eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJsb25nYnJpZGdlIiwic3ViIjoiYWNjZXNzX3Rva2VuIiwiZXhwIjoxNzYzNDU3NzkyLCJpYXQiOjE3NTU2ODE5MTUsImFrIjoiOWVjOWM4ZTYwZDhhOTI1ZmVjYTIwMTJmZGZjNDY2NjgiLCJhYWlkIjoyMDYwMTIwMSwiYWMiOiJoa19iZXRhIiwibWlkIjoxODIwNTY3OSwic2lkIjoidko2K2J4WjhKWEVWemM4OWVlMytUQT09IiwiYmwiOjIsInVsIjowLCJpayI6ImhrX2JldGFfMjA2MDEyMDEifQ.fC7T2Uv_Fr5OtSUy5-wDCUivjP3Z-i1FphEskMN5_TQuVMpKAD_x2k9nendLmNtaj3d3LHgyO5Y06mO9K2IIr6FBeu7NYGummJE0efDXqSJOXrNBaErXrWPNzN21L0N9Vvx0xw4RUVkUGiTkuEdri7nobBjXQ7bRIgT6CZ6g77moqJWRNaq05nbEBVevMpGHtc4I5WYBYyvHGBE-Eb-vHbaILPid7gfEnnnwMaBFM4AlFHFIl-FGdYTnbAsM3SBXF4OXyG7S2cBNhB2aZb7ORAAyO071oqBWWzjdRnMHjyijYLc3i9MU-V7N1htpaqJ_vQiz8GBkG0t0ww0GosAJ67LYi4G2Fqpr45xF1oCluBzSbvMssMhmpfBxCA_AGgZX_bMWsgzuw3fGLTSH2plDY0PeYeLKKcR8ql556vlSWnlooenT4JnsQfgJtsa1r9KLMc-UEZy3M23qRTzwrJl5A1aw30gaynKY-guQFh8Av5vGMtS1CMHu55LI4gezC-kPcM7GXdTuf_zFP1Gu--oGE3zWuEfMx3_-RHWmmK6WhDJ8_qldJqqP7i7TE8QpfGUMWBk_-EJI0FI8_yBqH1zc-cPLaruwB2QP58Ni8QTj10xbuzbdtzA0JTGq1zexLr6dywTgNOpVm8wvnXUBxQVgqTtHbDv3D5dtm1yG9OejkDk\"):\n", "        \"\"\"初始化长桥交易连接\"\"\"\n", "        self.config = Config(app_key=app_key, app_secret=app_secret, access_token=access_token)\n", "        self.trd_ctx = TradeContext(self.config)\n", "        self.order_status_handler = None\n", "        self._is_subscribed = False\n", "\n", "    def query_funds(self) -> Optional[pd.DataFrame]:\n", "        \"\"\"查询资金信息\"\"\"\n", "        try:\n", "            resp = self.trd_ctx.account_balance()\n", "            if resp:\n", "                funds_data = []\n", "                for balance in resp:\n", "                    funds_data.append({\n", "                        'total_cash': float(balance.total_cash),\n", "                        'max_finance_amount': float(balance.max_finance_amount),\n", "                        'remaining_finance_amount': float(balance.remaining_finance_amount),\n", "                        'net_assets': float(balance.net_assets),\n", "                        'buy_power': float(balance.buy_power),\n", "                        'currency': balance.currency\n", "                    })\n", "                return pd.DataFrame(funds_data)\n", "            else:\n", "                lg.error('资金查询失败: 无响应数据')\n", "                return None\n", "        except Exception as e:\n", "            lg.error(f'资金查询异常：{e}')\n", "            return None\n", "    \n", "    def query_position(self) -> Optional[pd.DataFrame]:\n", "        \"\"\"查询持仓信息\"\"\"\n", "        try:\n", "            resp = self.trd_ctx.stock_positions()\n", "            if resp and resp.channels:\n", "                positions_data = []\n", "                for channel in resp.channels:\n", "                    for position in channel.positions:\n", "                        positions_data.append({\n", "                            'symbol': position.symbol,\n", "                            'symbol_name': position.symbol_name,\n", "                            'quantity': int(position.quantity),\n", "                            'available_quantity': int(position.available_quantity),\n", "                            'market_value': float(position.market_value),\n", "                            'average_cost': float(position.average_cost) if position.average_cost else 0.0,\n", "                            'unrealized_pnl': float(position.unrealized_pnl) if position.unrealized_pnl else 0.0,\n", "                            'unrealized_pnl_rate': float(position.unrealized_pnl_rate) if position.unrealized_pnl_rate else 0.0\n", "                        })\n", "                return pd.DataFrame(positions_data) if positions_data else pd.DataFrame()\n", "            else:\n", "                lg.info('持仓查询成功: 无持仓')\n", "                return pd.DataFrame()\n", "        except Exception as e:\n", "            lg.error(f'持仓查询异常: {e}')\n", "            return None\n", "        \n", "    def place_order(self, symbol: str, side: str, quantity: int,price: float) -> Optional[str]:\n", "        \"\"\"\n", "        下单\n", "        Args:\n", "            symbol: 股票代码 (如 \"AAPL.US\")\n", "            side: 买卖方向 (\"Buy\" 或 \"Sell\")\n", "            quantity: 数量\n", "            price: 价格\n", "        Returns:\n", "            订单ID或None\n", "        \"\"\"\n", "        try:\n", "            # 转换参数\n", "            order_side = OrderSide.Buy if side.lower() == \"buy\" else OrderSide.Sell\n", "            \n", "            resp = self.trd_ctx.submit_order(\n", "                symbol=symbol,\n", "                order_type=OrderType.LO,\n", "                side=order_side,\n", "                submitted_quantity=Decimal(quantity),\n", "                time_in_force=TimeInForceType.Day,\n", "                submitted_price=Decimal(price)\n", "            )\n", "\n", "            if resp and resp.order_id:\n", "                lg.info(f'下单成功：{resp.order_id}')\n", "                return resp.order_id\n", "            else:\n", "                lg.error('下单失败：无订单ID返回')\n", "                return None\n", "        except Exception as e:\n", "            lg.error(f'下单异常：{e}')\n", "            return None\n", "    \n", "    def cancel_order(self, order_id: str) -> bool:\n", "        \"\"\"撤单\"\"\"\n", "        try:\n", "            self.trd_ctx.cancel_order(order_id)\n", "            lg.info(f'撤单成功: {order_id}')\n", "            return True\n", "        except Exception as e:\n", "            lg.error(f'撤单异常: {e}')\n", "            return False\n", "            \n", "    def replace_order(self, order_id: str, quantity: int = None, price: float = None) -> bool:\n", "        \"\"\"改单\"\"\"\n", "        try:\n", "            kwargs = {'order_id': order_id}\n", "            if quantity is not None:\n", "                kwargs['quantity'] = Decimal(str(quantity))\n", "            if price is not None:\n", "                kwargs['price'] = Decimal(str(price))\n", "                \n", "            self.trd_ctx.replace_order(**kwargs)\n", "            lg.info(f'改单成功: {order_id}')\n", "            return True\n", "        except Exception as e:\n", "            lg.error(f'改单异常: {e}')\n", "            return False\n", "    \n", "    def query_today_orders(self) -> Optional[pd.DataFrame]:\n", "        \"\"\"查询当日订单\"\"\"\n", "        try:\n", "            resp = self.trd_ctx.stock_positions()\n", "            if resp:\n", "                positions_data = []\n", "                for position in resp:\n", "                    positions_data.append({\n", "                        'symbol': position.symbol,\n", "                        'symbol_name': position.symbol_name,\n", "                        'quantity': int(position.quantity),\n", "                        'available_quantity': int(position.available_quantity),\n", "                        'market_value': float(position.market_value),\n", "                        'average_cost': float(position.average_cost) if position.average_cost else 0.0,\n", "                        'unrealized_pnl': float(position.unrealized_pnl) if position.unrealized_pnl else 0.0,\n", "                        'unrealized_pnl_rate': float(position.unrealized_pnl_rate) if position.unrealized_pnl_rate else 0.0\n", "                    })\n", "                return pd.DataFrame(positions_data) if positions_data else pd.DataFrame()\n", "            else:\n", "                return pd.DataFrame()\n", "        except Exception as e:\n", "            lg.error(f'当日订单查询异常: {e}')\n", "            return None\n", "        \n", "    def set_order_status_handler(self, callback: Callable) -> bool:\n", "        \"\"\"设置订单状态变化处理器\"\"\"\n", "        try:\n", "            self.order_status_handler = OrderstatusHandler(callback)\n", "\n", "            # 设置订单状态变化回调\n", "            self.trd_ctx.set_on_order_changed(self.order_status_handler.handle_order_changed)\n", "\n", "            # 订阅私有频道\n", "            if not self._is_subscribed:\n", "                self.trd_ctx.subscribe([TopicType.Private])\n", "                self._is_subscribed = True\n", "                lg.info(\"订单状态变化监听器注册成功\")\n", "                return True\n", "            \n", "        except Exception as e:\n", "            lg.error(f\"订单状态变化监听器注册失败：{e}\")\n", "            return False\n", "    \n", "    def remove_order_status_handler(self) -> bool:\n", "        \"\"\"移除订单状态变化处理器\"\"\"\n", "        try:\n", "            if self.order_status_handler:\n", "                self.trd_ctx.unsubscribe([TopicType.Private])\n", "                self._is_subscribed = False\n", "\n", "            self.order_status_handler = None\n", "            lg.info(\"订单状态变化监听器已移除\")\n", "            return True\n", "        except Exception as e:\n", "            lg.error(f\"订单状态变化监听器移除失败: {e}\")\n", "            return False\n", "        \n", "    def close(self):\n", "        \"\"\"关闭交易连接\"\"\"\n", "        try:\n", "            # 移除事件处理器\n", "            self.remove_order_status_handler()\n", "            lg.info(\"长桥交易连接已关闭\")\n", "        except Exception as e:\n", "            lg.error(f\"关闭连接异常: {e}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "910028f131c8d1bf", "metadata": {"ExecuteTime": {"end_time": "2025-08-26T02:42:33.785888Z", "start_time": "2025-08-26T02:42:31.914367Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip freeze > requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "54e3bd51e0ce092a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}