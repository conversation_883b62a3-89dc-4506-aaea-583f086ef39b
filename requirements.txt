absl-py==2.2.2
anyio @ file:///C:/b/abs_847uobe7ea/croot/anyio_1706220224037/work
appdirs==1.4.4
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///C:/ci/argon2-cffi-bindings_1644569878360/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
astunparse==1.6.3
async-lru @ file:///C:/b/abs_e0hjkvwwb5/croot/async-lru_1699554572212/work
attrs @ file:///C:/b/abs_24xj44kobp/croot/attrs_1729089434205/work
Babel @ file:///C:/b/abs_a2shv_3tqi/croot/babel_1671782804377/work
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
backtrader==1.9.78.123
beautifulsoup4 @ file:///C:/b/abs_d5wytg_p0w/croot/beautifulsoup4-split_1718029833749/work
bleach @ file:///opt/conda/conda-bld/bleach_1641577558959/work
Bottleneck @ file:///C:/b/abs_f05kqh7yvj/croot/bottleneck_1707864273291/work
Brotli @ file:///C:/b/abs_3d36mno480/croot/brotli-split_1714483178642/work
bs4==0.0.2
cachetools==5.5.2
certifi @ file:///C:/b/abs_1fw_exq1si/croot/certifi_1725551736618/work/certifi
cffi @ file:///C:/b/abs_90yq4lmu83/croot/cffi_1726856448345/work
chardet==3.0.4
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.1.8
cloudpickle==3.1.1
colorama @ file:///C:/b/abs_a9ozq0l032/croot/colorama_1672387194846/work
colorlover==0.3.0
comm @ file:///C:/b/abs_67a8058udb/croot/comm_1709322909844/work
contourpy @ file:///C:/b/abs_d5rpy288vc/croots/recipe/contourpy_1663827418189/work
cramjam==2.9.0
cufflinks==0.17.3
curl_cffi==0.9.0
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
Cython @ file:///C:/b/abs_c0003o_tam/croot/cython_1727769818780/work
DateTime==5.5
debugpy @ file:///C:/b/abs_c0y1fjipt2/croot/debugpy_1690906864587/work
decorator @ file:///opt/conda/conda-bld/decorator_1643638310831/work
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
deprecation==2.1.0
eikon==1.1.18
entrypoints @ file:///C:/ci/entrypoints_1649926621247/work
et-xmlfile==1.1.0
exceptiongroup @ file:///C:/b/abs_c5h1o1_b5b/croot/exceptiongroup_1706031441653/work
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
fastjsonschema @ file:///C:/Users/<USER>/AppData/Local/Temp/abs_ebruxzvd08/croots/recipe/python-fastjsonschema_1661376484940/work
fastparquet==2024.2.0
filelock==3.16.1
flatbuffers==25.2.10
fonttools @ file:///C:/b/abs_f47gnfqnx0/croot/fonttools_1713551644747/work
frozendict==2.4.6
fsspec==2024.10.0
futu_api==9.3.5308
future @ file:///C:/b/abs_3dcibf18zi/croot/future_1677599891380/work
gast==0.4.0
google-auth==2.40.1
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
graphviz==0.20.3
grpcio @ file:///C:/b/abs_7d9olmtnd2/croot/grpc-split_1716834609196/work
gym==0.26.2
gym-notices==0.0.8
h11 @ file:///C:/b/abs_1czwoyexjf/croot/h11_1706652332846/work
h2==4.1.0
h5py==3.11.0
hpack==4.0.0
httpcore @ file:///C:/b/abs_55n7g233bw/croot/httpcore_1706728507241/work
httpx @ file:///C:/b/abs_43e135shby/croot/httpx_1723474830126/work
hyperframe==6.0.1
idna==2.10
importlib-metadata @ file:///C:/b/abs_c1egths604/croot/importlib_metadata-suite_1704813568388/work
importlib_resources @ file:///C:/b/abs_e9f8_0_2dq/croot/importlib_resources-suite_1720641112288/work
ipykernel @ file:///C:/b/abs_5etv6sld_j/croot/ipykernel_1728665608850/work
ipython @ file:///C:/b/abs_254uk73z5b/croot/ipython_1691532131313/work
ipywidgets==8.1.5
jedi @ file:///C:/b/abs_1b8kmj7rrm/croot/jedi_1721058359741/work
Jinja2 @ file:///C:/b/abs_92fccttino/croot/jinja2_1716993447201/work
joblib @ file:///C:/b/abs_f4b98l6lgk/croot/joblib_1718217224240/work
json5 @ file:///tmp/build/80754af9/json5_1624432770122/work
jsonschema @ file:///C:/b/abs_394_t6__xq/croot/jsonschema_1728486718320/work
jsonschema-specifications @ file:///C:/b/abs_0brvm6vryw/croot/jsonschema-specifications_1699032417323/work
jupyter-events @ file:///C:/b/abs_c2m9s5b5m5/croot/jupyter_events_1718738115254/work
jupyter-lsp @ file:///C:/b/abs_ecle3em9d4/croot/jupyter-lsp-meta_1699978291372/work
jupyter_client @ file:///C:/b/abs_d8fk_kz9zk/croot/jupyter_client_1676330195659/work
jupyter_core @ file:///C:/b/abs_beftpbuevw/croot/jupyter_core_1718818307097/work
jupyter_server @ file:///C:/b/abs_9a333nh6yu/croot/jupyter_server_1718827092223/work
jupyter_server_terminals @ file:///C:/b/abs_ec0dq4b50j/croot/jupyter_server_terminals_1686870763512/work
jupyterlab @ file:///C:/b/abs_88qjk9lo2r/croot/jupyterlab_1725895228333/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab_server @ file:///C:/b/abs_fdi5r_tpjc/croot/jupyterlab_server_1725865372811/work
jupyterlab_widgets==3.0.13
keras==2.13.1
kiwisolver @ file:///C:/b/abs_88mdhvtahm/croot/kiwisolver_1672387921783/work
libclang==18.1.1
lightgbm==4.5.0
llvmlite @ file:///C:/b/abs_458aeunq7a/croot/llvmlite_1697031108211/work
longport==3.0.7
lxml==5.3.2
Markdown==3.7
MarkupSafe @ file:///C:/b/abs_ecfdqh67b_/croot/markupsafe_1704206030535/work
matplotlib @ file:///C:/b/abs_085jhivdha/croot/matplotlib-suite_1693812524572/work
matplotlib-inline @ file:///C:/ci/matplotlib-inline_1661934035815/work
mistune @ file:///C:/Users/<USER>/AppData/Local/Temp/abs_081kimkskf/croots/recipe/mistune_1661496225923/work
mkl-fft==1.3.1
mkl-random @ file:///C:/ci/mkl_random_1626186184278/work
mkl-service==2.4.0
mpmath==1.3.0
multitasking==0.0.11
nbclient @ file:///C:/b/abs_cal0q5fyju/croot/nbclient_1698934263135/work
nbconvert @ file:///C:/b/abs_ac6qnzi3no/croot/nbconvert_1728050663985/work
nbformat @ file:///C:/b/abs_c2jkw46etm/croot/nbformat_1728050303821/work
nest-asyncio @ file:///C:/b/abs_65d6lblmoi/croot/nest-asyncio_1708532721305/work
networkx==3.1
nltk==3.9.1
notebook @ file:///C:/b/abs_feeub5ouq6/croot/notebook_1727197380211/work
notebook_shim @ file:///C:/b/abs_a5xysln3lb/croot/notebook-shim_1699455926920/work
numba @ file:///C:/b/abs_2eee8kaps6/croot/numba_1701362407655/work
numexpr @ file:///C:/b/abs_a7kbak88hk/croot/numexpr_1668713882979/work
numpy @ file:///C:/b/abs_54abayvc9j/croot/numpy_and_numpy_base_1682520598361/work
oauthlib==3.2.2
openpyxl @ file:///C:/b/abs_0e6ca21lac/croot/openpyxl_1721752965859/work
opt_einsum==3.4.0
overrides @ file:///C:/b/abs_cfh89c8yf4/croot/overrides_1699371165349/work
packaging @ file:///C:/b/abs_c3vlh0z4jw/croot/packaging_1720101866539/work
pandas @ file:///C:/miniconda3/conda-bld/pandas_1692299636855/work
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
parso @ file:///opt/conda/conda-bld/parso_1641458642106/work
patsy==1.0.1
peewee==3.18.1
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pillow @ file:///C:/b/abs_32o8er3uqp/croot/pillow_1721059447598/work
pkgutil_resolve_name @ file:///C:/b/abs_4cck6bcc2k/croot/pkgutil-resolve-name_1704297519576/work
platformdirs @ file:///C:/b/abs_b6z_yqw_ii/croot/platformdirs_1692205479426/work
plotly==5.24.1
ply==3.11
polars==1.8.2
polygon-api-client==1.15.3
pooch @ file:///tmp/build/80754af9/pooch_1623324770023/work
prometheus-client @ file:///C:/Windows/TEMP/abs_ab9nx8qb08/croots/recipe/prometheus_client_1659455104602/work
prompt-toolkit @ file:///C:/b/abs_68uwr58ed1/croot/prompt-toolkit_1704404394082/work
protobuf==3.20.3
psutil @ file:///C:/Windows/Temp/abs_b2c2fd7f-9fd5-4756-95ea-8aed74d0039flsd9qufz/croots/recipe/psutil_1656431277748/work
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py-cpuinfo @ file:///C:/b/abs_9ej7u6shci/croot/py-cpuinfo_1698068121579/work
py4j==********
pyarrow==17.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pycryptodome==3.22.0
Pygments @ file:///C:/b/abs_fay9dpq4n_/croot/pygments_1684279990574/work
pyparsing @ file:///C:/Users/<USER>/AppData/Local/Temp/abs_7f_7lba6rl/croots/recipe/pyparsing_1661452540662/work
PyQt5==5.15.10
PyQt5-sip @ file:///C:/b/abs_c0pi2mimq3/croot/pyqt-split_1698769125270/work/pyqt_sip
PySocks @ file:///C:/ci/pysocks_1605287845585/work
pyspark==3.5.3
python-dateutil @ file:///C:/b/abs_3au_koqnbs/croot/python-dateutil_1716495777160/work
python-json-logger @ file:///C:/b/abs_cblnsm6puj/croot/python-json-logger_1683824130469/work
pytz @ file:///C:/b/abs_6ap4tsz1ox/croot/pytz_1713974360290/work
pywin32==305.1
pywinpty @ file:///C:/b/abs_73vshmevwq/croot/pywinpty_1677609966356/work/target/wheels/pywinpty-2.0.10-cp38-none-win_amd64.whl
PyYAML @ file:///C:/b/abs_14xkfs39bx/croot/pyyaml_1728657968772/work
pyzmq @ file:///C:/b/abs_89aq69t0up/croot/pyzmq_1705605705281/work
referencing @ file:///C:/b/abs_09f4hj6adf/croot/referencing_1699012097448/work
regex==2024.11.6
requests @ file:///C:/b/abs_9frifg92q2/croot/requests_1721410901096/work
requests-oauthlib==2.0.0
rfc3339-validator @ file:///C:/b/abs_ddfmseb_vm/croot/rfc3339-validator_1683077054906/work
rfc3986==1.5.0
rfc3986-validator @ file:///C:/b/abs_6e9azihr8o/croot/rfc3986-validator_1683059049737/work
rpds-py @ file:///C:/b/abs_76j4g4la23/croot/rpds-py_1698947348047/work
rsa==4.9.1
scikit-learn @ file:///C:/b/abs_daon7wm2p4/croot/scikit-learn_1694788586973/work
scipy==1.10.1
seaborn==0.13.2
Send2Trash @ file:///C:/b/abs_08dh49ew26/croot/send2trash_1699371173324/work
simplejson==3.20.1
sip @ file:///C:/b/abs_edevan3fce/croot/sip_1698675983372/work
six @ file:///tmp/build/80754af9/six_1644875935023/work
sniffio @ file:///C:/b/abs_3akdewudo_/croot/sniffio_1705431337396/work
soupsieve @ file:///C:/b/abs_bbsvy9t4pl/croot/soupsieve_1696347611357/work
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
statsmodels==0.14.1
sympy==1.13.3
tables @ file:///C:/b/abs_0626auep9v/croot/pytables_1691623892917/work
tabulate==0.9.0
tenacity==9.0.0
tensorboard==2.13.0
tensorboard-data-server==0.7.2
tensorflow==2.13.0
tensorflow-estimator==2.13.0
tensorflow-intel==2.13.0
tensorflow-io-gcs-filesystem==0.31.0
termcolor==2.4.0
terminado @ file:///C:/b/abs_25nakickad/croot/terminado_1671751845491/work
textblob==0.18.0.post0
threadpoolctl @ file:///C:/b/abs_def0dwqlft/croot/threadpoolctl_1719407816649/work
tinycss2 @ file:///C:/b/abs_52w5vfuaax/croot/tinycss2_1668168823131/work
tomli @ file:///C:/Windows/TEMP/abs_ac109f85-a7b3-4b4d-bcfd-52622eceddf0hy332ojo/croots/recipe/tomli_1657175513137/work
torch @ file:///C:/Users/<USER>/Downloads/torch-2.4.1%2Bcu118-cp38-cp38-win_amd64.whl#sha256=1520c0a9aa6d0187c9617b07409c9493d0bf20b28f26cffa3458995f53f58c48
tornado @ file:///C:/b/abs_7bua0304mj/croot/tornado_1718740122405/work
tqdm==4.67.1
traitlets @ file:///C:/b/abs_bfsnoxl4pq/croot/traitlets_1718227069245/work
tushare==1.4.21
typing_extensions==4.5.0
tzdata @ file:///croot/python-tzdata_1690578112552/work
unicodedata2 @ file:///C:/b/abs_b6apldlg7y/croot/unicodedata2_1713212998255/work
urllib3 @ file:///C:/b/abs_9a_f8h_bn2/croot/urllib3_1727769836930/work
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webencodings==0.5.1
websocket-client @ file:///C:/b/abs_5dmnxxoci9/croot/websocket-client_1715878351319/work
websockets==13.1
Werkzeug==3.0.6
widgetsnbextension==4.0.13
win-inet-pton @ file:///C:/ci/win_inet_pton_1605306167264/work
wrapt==1.17.2
xgboost==2.1.4
xlrd @ file:///croot/xlrd_1685030938141/work
yfinance==0.2.61
zipp @ file:///C:/b/abs_b0beoc27oa/croot/zipp_1704206963359/work
zope.interface==7.2
