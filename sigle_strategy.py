from futu import *
import pandas as pd
import queue, threading
from typing import Callable, Optional
from datetime import datetime, time, date, timedelta
import pytz
import logging as lg
from longport.openapi import TradeContext, Config, OrderType, OrderSide, TimeInForceType, PushOrderChanged, TopicType
from decimal import Decimal
import json
import os

# ===== 日志配置 =====
log_filename = f'grid_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
lg.basicConfig(
    level=lg.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        lg.FileHandler(log_filename, encoding='utf-8'),
        lg.StreamHandler()
    ]
)


# ===== 数据接口 =====
class StackQuote(StockQuoteHandlerBase):
    """实时行情回调处理器"""

    def __init__(self, callback):
        super().__init__()
        self.callback = callback

    @staticmethod
    def get_us_trading_session():
        """获取当前美东交易阶段"""
        eastern = pytz.timezone('US/Eastern')
        now = datetime.now(eastern).time()

        if time(4, 0) <= now < time(9, 30):
            return 'pre'
        elif time(9, 30) <= now < time(16, 0):
            return 'regular'
        elif time(16, 0) <= now < time(20, 0):
            return 'after'
        else:
            return 'overnight'

    @classmethod
    def extract_real_price(cls, row):
        """根据交易时段提取当前应使用的价格字段"""
        session = cls.get_us_trading_session()

        if session == 'pre':
            return row.get('pre_price', row.get('last_price', None)), '盘前'
        elif session == 'regular':
            return row.get('last_price', None), '盘中'
        elif session == 'after':
            return row.get('after_price', row.get('last_price', None)), '盘后'
        else:
            return row.get('last_price', None), '隔夜'

    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super().on_recv_rsp(rsp_pb)
        if ret_code != RET_OK or data.empty:
            return ret_code, data

        try:
            row = data.iloc[0]
            price, stage = self.extract_real_price(row)

            if price is None or pd.isna(price):
                lg.warning(f"获取到空价格数据: {row.to_dict()}")
                return ret_code, data

            tick = {
                'code': row['code'],
                'price': float(price),
                'stage': stage,
                'data_time': datetime.now(pytz.timezone('US/Eastern')).time()
            }
            self.callback(tick)
        except Exception as e:
            lg.error(f"实时数据处理异常: {e}")
            return RET_ERROR, str(e)

        return RET_OK, data


class DataFeed:
    """统一数据接口模块"""

    def __init__(self, host='127.0.0.1', port=11111):
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
        self._realtime_thread = None
        self._running = False
        self._data_queue = queue.Queue()

    def __del__(self):
        try:
            self.quote_ctx.close()
        except:
            pass

    def get_daily_data(self, symbol, start, end):
        """获取历史日线数据"""
        ret, data, _ = self.quote_ctx.request_history_kline(
            symbol, start=start, end=end, ktype=KLType.K_DAY)
        if ret != RET_OK:
            raise RuntimeError(f"历史K线获取失败：{data}")

        if data.empty:
            lg.warning(f"[警告] {symbol} 在 {start}~{end} 无数据")
            return pd.DataFrame()

        # 保存到CSV文件
        csv_filename = f'{symbol.split(".")[-1]}.csv'
        data.to_csv(csv_filename, index=False)
        lg.info(f"历史数据已更新：{csv_filename}")
        return data

    def start_realtime_quote(self, symbol, on_tick):
        """订阅并启动实时行情流监听"""
        handler = StackQuote(on_tick)
        self.quote_ctx.set_handler(handler)

        ret, err = self.quote_ctx.subscribe(symbol, [SubType.QUOTE])
        if ret != RET_OK:
            raise RuntimeError(f"订阅失败: {err}")

        self._running = True
        lg.info(f"🚀 开始监听 {symbol} 的实时行情...")

    def stop_realtime_quote(self, symbol):
        """手动停止实时行情监听"""
        self._running = False
        try:
            self.quote_ctx.unsubscribe(symbol, [SubType.QUOTE])
        except:
            pass

    def get_stock_quote(self, symbol: str, on_tick_callback: Optional[Callable[[dict], None]] = None):
        """实时驱动系统接口"""
        def queue_handler(tick_data):
            self._data_queue.put(tick_data)

        handler = StackQuote(queue_handler)
        self.quote_ctx.set_handler(handler)

        ret, err = self.quote_ctx.subscribe(symbol, [SubType.QUOTE])
        if ret != RET_OK:
            raise RuntimeError(f"订阅失败: {err}")

        self._running = True
        lg.info(f"🚀 开始实时驱动 {symbol} 行情...")

        while self._running:
            try:
                data = self._data_queue.get(timeout=1)
                if on_tick_callback:
                    on_tick_callback(data)
                else:
                    lg.info(f"📈 收到行情数据: {data}")

            except queue.Empty:
                continue
            except KeyboardInterrupt:
                lg.info("⏹️  用户中断，停止行情监听...")
                break
            except Exception as e:
                lg.error(f"❌ 行情处理异常: {e}")
                continue

        self.stop_realtime_quote(symbol)


# ===== 交易接口 =====
class OrderStatusHandler:
    """订单状态变化处理器"""

    def __init__(self, callback: Callable):
        self.callback = callback

    def handle_order_changed(self, event: PushOrderChanged):
        """处理订单状态变化推送"""
        try:
            order_status_info = {
                'order_id': event.order_id,
                'code': event.symbol,
                'order_status': str(event.status),
                'order_type': str(event.order_type),
                'side': str(event.side) if hasattr(event, 'side') else None,
                'executed_quantity': int(event.executed_quantity) if hasattr(event, 'executed_quantity') else 0,
                'executed_price': float(event.executed_price) if hasattr(event, 'executed_price') else 0.0,
                'qty': int(event.quantity) if hasattr(event, 'quantity') else 0,
                'dealt_qty': int(event.executed_quantity) if hasattr(event, 'executed_quantity') else 0
            }

            lg.info(f"订单状态更新：{order_status_info}")
            if self.callback:
                self.callback(order_status_info)

        except Exception as e:
            lg.error(f"订单状态回调函数执行异常：{e}")


class Trader:
    """长桥交易接口"""

    def __init__(self, app_key: str, app_secret: str, access_token: str):
        """初始化长桥交易连接"""
        self.config = Config(app_key=app_key, app_secret=app_secret, access_token=access_token)
        self.trd_ctx = TradeContext(self.config)
        self.order_status_handler = None
        self._is_subscribed = False

    def query_funds(self) -> Optional[pd.DataFrame]:
        """查询资金信息"""
        try:
            resp = self.trd_ctx.account_balance()
            if resp:
                funds_data = []
                for balance in resp:
                    funds_data.append({
                        'total_cash': float(balance.total_cash),
                        'max_finance_amount': float(balance.max_finance_amount),
                        'remaining_finance_amount': float(balance.remaining_finance_amount),
                        'net_assets': float(balance.net_assets),
                        'buy_power': float(balance.buy_power),
                        'currency': balance.currency
                    })
                return pd.DataFrame(funds_data)
            else:
                lg.error('资金查询失败: 无响应数据')
                return None
        except Exception as e:
            lg.error(f'资金查询异常：{e}')
            return None

    def query_position(self) -> Optional[pd.DataFrame]:
        """查询持仓信息"""
        try:
            resp = self.trd_ctx.stock_positions()
            if resp and resp.channels:
                positions_data = []
                for channel in resp.channels:
                    for position in channel.positions:
                        positions_data.append({
                            'symbol': position.symbol,
                            'symbol_name': position.symbol_name,
                            'quantity': int(position.quantity),
                            'available_quantity': int(position.available_quantity),
                            'market_value': float(position.market_value),
                            'average_cost': float(position.average_cost) if position.average_cost else 0.0,
                            'unrealized_pnl': float(position.unrealized_pnl) if position.unrealized_pnl else 0.0,
                            'unrealized_pnl_rate': float(
                                position.unrealized_pnl_rate) if position.unrealized_pnl_rate else 0.0
                        })
                return pd.DataFrame(positions_data) if positions_data else pd.DataFrame()
            else:
                lg.info('持仓查询成功: 无持仓')
                return pd.DataFrame()
        except Exception as e:
            lg.error(f'持仓查询异常: {e}')
            return None

    def place_order(self, symbol: str, side: str, quantity: int, price: float) -> Optional[str]:
        """
        下单 - 修正参数名称
        Args:
            symbol: 股票代码 (如 "SQQQ.US")
            side: 买卖方向 ("Buy" 或 "Sell")
            quantity: 数量
            price: 价格
        Returns:
            订单ID或None
        """
        try:
            order_side = OrderSide.Buy if side.lower() == "buy" else OrderSide.Sell

            resp = self.trd_ctx.submit_order(
                symbol=symbol,
                order_type=OrderType.LO,
                side=order_side,
                submitted_quantity=Decimal(str(quantity)),
                time_in_force=TimeInForceType.Day,
                submitted_price=Decimal(str(price))
            )

            if resp and resp.order_id:
                lg.info(f'下单成功：{resp.order_id}')
                return resp.order_id
            else:
                lg.error('下单失败：无订单ID返回')
                return None
        except Exception as e:
            lg.error(f'下单异常：{e}')
            return None

    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        try:
            self.trd_ctx.cancel_order(order_id)
            lg.info(f'撤单成功: {order_id}')
            return True
        except Exception as e:
            lg.error(f'撤单异常: {e}')
            return False

    def replace_order(self, order_id: str, quantity: int = None, price: float = None) -> bool:
        """改单"""
        try:
            kwargs = {'order_id': order_id}
            if quantity is not None:
                kwargs['quantity'] = Decimal(str(quantity))
            if price is not None:
                kwargs['price'] = Decimal(str(price))

            self.trd_ctx.replace_order(**kwargs)
            lg.info(f'改单成功: {order_id}')
            return True
        except Exception as e:
            lg.error(f'改单异常: {e}')
            return False

    def set_order_status_handler(self, callback: Callable) -> bool:
        """设置订单状态变化处理器"""
        try:
            self.order_status_handler = OrderStatusHandler(callback)
            self.trd_ctx.set_on_order_changed(self.order_status_handler.handle_order_changed)

            if not self._is_subscribed:
                self.trd_ctx.subscribe([TopicType.Private])
                self._is_subscribed = True
                lg.info("订单状态变化监听器注册成功")
                return True

        except Exception as e:
            lg.error(f"订单状态变化监听器注册失败：{e}")
            return False

    def remove_order_status_handler(self) -> bool:
        """移除订单状态变化处理器"""
        try:
            if self.order_status_handler:
                self.trd_ctx.unsubscribe([TopicType.Private])
                self._is_subscribed = False

            self.order_status_handler = None
            lg.info("订单状态变化监听器已移除")
            return True
        except Exception as e:
            lg.error(f"订单状态变化监听器移除失败: {e}")
            return False

    def close(self):
        """关闭交易连接"""
        try:
            self.remove_order_status_handler()
            lg.info("长桥交易连接已关闭")
        except Exception as e:
            lg.error(f"关闭连接异常: {e}")


# ===== 交易动作定义 =====
class TradeAction:
    SELL = 'SELL'
    BUY = 'BUY'


# ===== 网格策略 =====
class IntegratedGridStrategy:
    """整合的网格交易策略类"""

    def __init__(self, csv_file: str, grid_levels: int = 15, step_pct: float = 0.015,
                 layer_offset: float = 0.005, qty_per_order: int = 30,
                 cover_discount: float = 0.985, lookback_days: int = 90):
        self.csv_file = csv_file
        self.grid_levels = grid_levels
        self.step_pct = step_pct
        self.layer_offset = layer_offset
        self.qty_per_order = qty_per_order
        self.cover_discount = cover_discount
        self.lookback_days = lookback_days

        self.csv_data = self._load_csv_data()
        self.grid_state: dict = {}
        self.base_price_L = 0.0
        self.current_position = 0
        self.last_grid_update_date = None
        self.order_to_level: dict = {}
        self.order_fill_tracker: dict = {}
        self.last_price = None

    def _load_csv_data(self) -> pd.DataFrame:
        """加载CSV数据并预处理"""
        try:
            if not os.path.exists(self.csv_file):
                lg.warning(f"CSV文件不存在: {self.csv_file}")
                return pd.DataFrame()

            df = pd.read_csv(self.csv_file)
            df['time_key'] = pd.to_datetime(df['time_key'])
            df['date'] = df['time_key'].dt.date
            return df
        except Exception as e:
            lg.error(f"加载CSV文件失败: {e}")
            return pd.DataFrame()

    def get_historical_low(self, target_date) -> float:
        """在指定日期之前，回看 lookback_days 天的最低价"""
        if self.csv_data.empty:
            lg.warning("CSV数据为空，无法计算历史最低价")
            return 0.0

        self.csv_data['low'] = pd.to_numeric(self.csv_data['low'], errors='coerce')
        df_filtered = self.csv_data[self.csv_data['date'] < target_date]
        if df_filtered.empty:
            lg.warning(f"没有找到 {target_date} 之前的数据")
            return 0.0

        daily_lows = df_filtered.groupby('date')['low'].min()
        recent_lows = daily_lows.tail(self.lookback_days).dropna()

        if recent_lows.empty:
            lg.warning("计算历史最低价失败")
            return 0.0

        return float(recent_lows.min())

    def initialize_grid(self, today_date):
        """程序启动时初始化网格"""
        if os.path.exists('grid_state.json'):
            self.load_grid_state('grid_state.json')
            lg.info("✅ 从历史状态文件加载网格配置")
            return

        self.base_price_L = self.get_historical_low(today_date)
        if self.base_price_L <= 0:
            lg.warning(f"⚠️ 无法获取有效的历史低点，日期: {today_date}")
            return

        lg.info(f"✅ 初始化网格，基准价: {self.base_price_L:.2f}")
        for n in range(1, self.grid_levels + 1):
            sell_price = self.base_price_L * (1 + self.layer_offset + n * self.step_pct)
            buy_price = sell_price * self.cover_discount
            self.grid_state[n] = {
                'sell_price': round(sell_price, 2),
                'buy_price': round(buy_price, 2),
                'status': 'waiting_sell',
                'position_qty': 0,
                'order_id': None,
                'last_signal_price': None
            }
        self.save_grid_state('grid_state.json')

    def _change_level_position(self, level: int, delta_qty: int) -> None:
        """更新层级持仓数量"""
        if level not in self.grid_state:
            lg.warning(f"警告: 未找到层级{level}的网格状态")
            return
        state = self.grid_state[level]
        state['position_qty'] = int(state.get('position_qty', 0)) + int(delta_qty)

    def update_grid(self, today_date) -> None:
        """每日刷新网格价位"""
        if self.last_grid_update_date == today_date:
            return

        self.base_price_L = float(self.get_historical_low(today_date))
        lg.info(f"🚀 开始刷新网格，基准价: {self.base_price_L:.2f}")

        for n, state in self.grid_state.items():
            sell_price = self.base_price_L * (1 + self.layer_offset + n * self.step_pct)
            buy_price = sell_price * self.cover_discount
            state['sell_price'] = round(sell_price, 2)
            state['buy_price'] = round(buy_price, 2)

        self.last_grid_update_date = today_date
        lg.info(f"网格更新完成，当前持仓: {self.current_position}")
        self.save_grid_state('grid_state.json')

    def check_sell_signals(self, current_price: float) -> list:
        """检查是否满足卖出信号条件"""
        signals = []
        for level, state in self.grid_state.items():
            if (state['status'] == 'waiting_sell' and
                    state['order_id'] is None and
                    current_price >= state['sell_price']):

                if state['last_signal_price'] != current_price:
                    signals.append({
                        'action': TradeAction.SELL,
                        'price': state['sell_price'],
                        'qty': self.qty_per_order,
                        'level': level
                    })
        return signals

    def check_buy_signals(self, current_price: float) -> list:
        """检查是否满足买入信号条件"""
        signals = []
        for level, state in self.grid_state.items():
            if (state['status'] == 'waiting_buy' and
                    state['order_id'] is None and
                    current_price <= state['buy_price']):

                if state['last_signal_price'] != current_price:
                    signals.append({
                        'action': TradeAction.BUY,
                        'price': state['buy_price'],
                        'qty': abs(state['position_qty']),
                        'level': level
                    })
        return signals

    def on_price(self, current_price: float) -> list:
        """根据当前价格判断是否触发交易信号"""
        if self.last_price == current_price or not self.grid_state:
            return []

        sell_signals = self.check_sell_signals(current_price)
        buy_signals = self.check_buy_signals(current_price)
        self.last_price = current_price

        return sell_signals + buy_signals

    def on_signal_processed(self, signal: dict, order_id: str) -> None:
        """信号处理完成回调"""
        level = signal['level']
        if level not in self.grid_state:
            return

        state = self.grid_state[level]

        if order_id:
            if signal['action'] == TradeAction.SELL:
                state['status'] = 'pending_sell'
            elif signal['action'] == TradeAction.BUY:
                state['status'] = 'pending_buy'

            state['order_id'] = order_id
            state['last_signal_price'] = signal['price']
            self.order_to_level[order_id] = level

            self.order_fill_tracker[order_id] = {
                'total_qty': signal['qty'],
                'filled_qty': 0,
                'level': level,
                'action': signal['action'],
                'avg_price': 0.0,
                'fill_count': 0
            }

            self.save_grid_state('grid_state.json')
            lg.info(f"层级{level}状态更新: {state['status']}, 订单ID: {order_id}")
        else:
            state['last_signal_price'] = None
            lg.error(f"层级{level}下单失败，状态保持: {state['status']}")

    def on_order_filled(self, order_id: str, fill_info: dict) -> bool:
        """处理订单成交回报"""
        if order_id not in self.order_to_level or order_id not in self.order_fill_tracker:
            lg.warning(f"警告: 未找到订单{order_id}的跟踪信息")
            return False

        level = self.order_to_level[order_id]
        if level not in self.grid_state:
            lg.warning(f"警告: 未找到层级{level}的网格状态")
            return False

        state = self.grid_state[level]
        tracker = self.order_fill_tracker[order_id]
        action = fill_info['action']
        fill_qty = int(fill_info['qty'])
        fill_price = float(fill_info['price'])

        # 更新成交跟踪信息
        tracker['filled_qty'] += fill_qty
        tracker['fill_count'] += 1

        # 计算平均成交价格
        if tracker['fill_count'] == 1:
            tracker['avg_price'] = fill_price
        else:
            prev_total_value = tracker['avg_price'] * (tracker['filled_qty'] - fill_qty)
            current_value = fill_price * fill_qty
            tracker['avg_price'] = (prev_total_value + current_value) / tracker['filled_qty']

        lg.info(f"层级{level}部分成交: {action} {fill_qty}股 @ {fill_price:.2f}")
        lg.info(f"   累计成交: {tracker['filled_qty']}/{tracker['total_qty']} 股，平均价: {tracker['avg_price']:.2f}")

        # 检查是否完全成交
        if tracker['filled_qty'] >= tracker['total_qty']:
            lg.info(f"🎯 层级{level}订单完全成交，开始更新网格状态")

            if action == 'SELL' and state['status'] == 'pending_sell':
                self.current_position -= tracker['filled_qty']
                self._change_level_position(level, -tracker['filled_qty'])
                state['status'] = 'waiting_buy'
                state['order_id'] = None
                state['last_signal_price'] = None
                lg.info(f"层级{level}卖空完成，平均价格: {tracker['avg_price']:.2f}, 当前持仓: {self.current_position}")

            elif action == 'BUY' and state['status'] == 'pending_buy':
                self.current_position += tracker['filled_qty']
                self._change_level_position(level, tracker['filled_qty'])
                state['status'] = 'waiting_sell'
                state['order_id'] = None
                state['last_signal_price'] = None
                lg.info(f"层级{level}买回完成，平均价格: {tracker['avg_price']:.2f}, 当前持仓: {self.current_position}")

            # 清理跟踪信息和订单映射
            del self.order_fill_tracker[order_id]
            del self.order_to_level[order_id]
            self.save_grid_state('grid_state.json')
            return True  # 完全成交
        else:
            lg.info(f"层级{level}部分成交，等待剩余 {tracker['total_qty'] - tracker['filled_qty']} 股成交")
            return False  # 部分成交

    def on_order_cancelled(self, order_id: str, cancelled_type: str = 'CANCELLED_ALL') -> None:
        """处理订单撤销"""
        if order_id not in self.order_to_level:
            lg.warning(f"警告: 未找到订单{order_id}对应的网格层级")
            return

        level = self.order_to_level[order_id]
        if level not in self.grid_state:
            lg.warning(f"警告: 未找到层级{level}的网格状态")
            return

        state = self.grid_state[level]

        if cancelled_type == 'CANCELLED_ALL':
            lg.info(f"订单{order_id}全部撤单，无部分成交处理")
            if state['status'] == 'pending_sell':
                state['status'] = 'waiting_sell'
            elif state['status'] == 'pending_buy':
                state['status'] = 'waiting_buy' if state.get('position_qty', 0) < 0 else 'waiting_sell'
        else:  # 部分成交撤单
            if order_id in self.order_fill_tracker:
                tracker = self.order_fill_tracker[order_id]
                filled_qty = tracker.get('filled_qty', 0)
                action = tracker.get('action', None)
                if filled_qty > 0:
                    lg.info(f"订单{order_id}撤销前已部分成交: {action} {filled_qty}股")
                    if state['status'] == 'pending_sell':
                        self.current_position -= filled_qty
                        self._change_level_position(level, -filled_qty)
                        state['status'] = 'waiting_buy'
                        lg.info(f"层级{level}部分卖空已成交，转为等待买回状态")
                    elif state['status'] == 'pending_buy':
                        self.current_position += filled_qty
                        self._change_level_position(level, filled_qty)

                        if state.get('position_qty', 0) < 0:
                            state['status'] = 'waiting_buy'
                            lg.info(f"层级{level}部分买回已成交，仍需买回剩余持仓")
                        else:
                            state['status'] = 'waiting_sell'
                            lg.info(f"层级{level}买回完成，恢复等待卖出状态")

                del self.order_fill_tracker[order_id]

        # 清理订单相关信息
        state['order_id'] = None
        state['last_signal_price'] = None
        del self.order_to_level[order_id]

        self.save_grid_state('grid_state.json')
        lg.info(f"层级{level}撤单处理完成，状态恢复为 {state['status']}")

    def get_grid_status(self) -> dict:
        """获取当前网格状态信息"""
        return {
            'base_price': self.base_price_L,
            'current_position': self.current_position,
            'grid_state': self.grid_state.copy(),
            'last_update_date': self.last_grid_update_date,
            'pending_orders_count': len(self.order_to_level),
            'pending_orders': self.order_to_level.copy()
        }

    def reset_position(self, new_position: int = 0) -> None:
        """重置持仓数量"""
        self.current_position = new_position
        lg.info(f"持仓重置为: {self.current_position}")

    def force_clear_pending_orders(self) -> None:
        """强制清除所有待成交订单状态"""
        cleared_count = 0
        for level, state in self.grid_state.items():
            if state['status'] in ['pending_sell', 'pending_buy']:
                # 根据是否已卖出恢复到正确状态
                if state.get('position_qty', 0) < 0:
                    state['status'] = 'waiting_buy'
                else:
                    state['status'] = 'waiting_sell'

                state['order_id'] = None
                state['last_signal_price'] = None
                cleared_count += 1

        self.order_to_level.clear()
        self.order_fill_tracker.clear()
        lg.info(f"强制清除了{cleared_count}个待成交订单状态和成交跟踪信息")

    def save_grid_state(self, file_path: str):
        """保存网格状态"""
        try:
            with open(file_path, 'w') as f:
                json.dump({str(k): v for k, v in self.grid_state.items()}, f, indent=4)
            lg.info(f"网格状态已经保存到{file_path}")
        except Exception as e:
            lg.error(f"保存网格状态失败: {e}")

    def load_grid_state(self, file_path: str):
        """加载网格状态"""
        if not os.path.exists(file_path):
            lg.info(f"网格状态文件不存在: {file_path}")
            return

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            self.grid_state = {int(k): v for k, v in data.items()}
            lg.info(f"网格状态已从{file_path}加载")
        except Exception as e:
            lg.error(f"加载网格状态失败: {e}")


# ===== 主控模块 =====
class MainController:
    def __init__(self, symbol: str, csv_file: str, app_key: str, app_secret: str, access_token: str):
        self.symbol = symbol
        self.today = self.get_us_date()
        self.delayed_timers = {}
        self.PARTIAL_FILL_DELAY = 2
        self.timer_lock = threading.Lock()

        # 模块初始化
        self.data_feed = DataFeed()
        self.strategy = IntegratedGridStrategy(csv_file)
        self.trader = Trader(app_key, app_secret, access_token)

        # 获取历史数据并保存
        try:
            end_date = self.today.strftime('%Y-%m-%d')
            start_date = (self.today - timedelta(days=90)).strftime('%Y-%m-%d')
            self.data_feed.get_daily_data(self.symbol, start_date, end_date)
        except Exception as e:
            lg.error(f"获取历史数据失败: {e}")

        # 网格状态初始化
        self.strategy.initialize_grid(self.today)

        # 注册回调事件处理器
        self.trader.set_order_status_handler(self.on_order_status_changed)

        lg.info(f"📊 网格策略初始状态:")
        self._print_grid_status()

    def start(self):
        lg.info(f"🚀 启动主交易控制器，标的: {self.symbol}")

        def on_tick(tick):
            try:
                if tick['price'] is None or tick['price'] <= 0:
                    lg.error("跳过无效行情数据")
                    return
                current_price = float(tick['price'])

                lg.info(f"📈 实时价格: {current_price} ({tick['stage']}) @ {tick['data_time']}")

                # 检查是否需要更新网格
                self._check_grid_update()

                # 获取交易信号
                signals = self.strategy.on_price(current_price)
                if not signals:
                    return

                # 执行所有信号
                for signal in signals:
                    self._execute_signal(signal)

            except Exception as e:
                lg.error(f"❌ 行情处理异常: {e}")

        # 启动行情监听
        self.data_feed.get_stock_quote(self.symbol, on_tick_callback=on_tick)

    def get_us_date(self):
        est = pytz.timezone('America/New_York')
        current_date = datetime.now(est).date()
        return current_date

    def _check_grid_update(self):
        """检查是否需要更新网格"""
        us_date = self.get_us_date()

        if us_date != self.today:
            lg.info(f"📅 日期变更，更新网格: {self.today} -> {us_date}")
            self.today = us_date
            end_date = self.today.strftime('%Y-%m-%d')
            start_date = (self.today - timedelta(days=90)).strftime('%Y-%m-%d')

            # 获取新数据并更新csv
            try:
                self.data_feed.get_daily_data(self.symbol, start_date, end_date)
                # 重新加载csv数据
                self.strategy.csv_data = self.strategy._load_csv_data()
                # 更新网格
                self.strategy.update_grid(self.today)
                self._print_grid_status()
            except Exception as e:
                lg.error(f"网格更新失败: {e}")

    def _execute_signal(self, signal: dict):
        """执行交易信号"""
        lg.info(f"✅ 触发交易信号: Level {signal['level']} - {signal['action']} {signal['qty']}@{signal['price']}")

        # 统一的动作映射
        if signal['action'] == TradeAction.SELL:
            side = 'Sell'
            action_desc = "卖空"
        elif signal['action'] == TradeAction.BUY:
            side = 'Buy'
            action_desc = "买回"
        else:
            lg.error(f"❌ 未知交易动作: {signal['action']}")
            return

        try:
            # 发起交易
            order_id = self.trader.place_order(
                symbol=self.symbol,
                side=side,
                quantity=signal['qty'],
                price=signal['price']
            )

            if order_id:
                lg.info(f"📤 {action_desc}订单已提交 - 订单ID: {order_id}")
                # 通知策略信号处理完成
                self.strategy.on_signal_processed(signal, order_id)
            else:
                lg.error(f"❌ {action_desc}下单失败")
                # 通知策略下单失败
                self.strategy.on_signal_processed(signal, None)

        except Exception as e:
            lg.error(f"❌ 下单异常: {e}")
            # 通知策略下单失败
            self.strategy.on_signal_processed(signal, None)

    def on_order_filled(self, fill_info: dict):
        """处理订单成交回报"""
        order_id = fill_info['order_id']

        lg.info(f"🎯 成交通知 | 订单ID: {order_id}")
        lg.info(f"   {fill_info['action'].upper()} {fill_info['qty']} 股 @ {fill_info['price']}")

        # 传递给策略处理，获取是否完全成交的状态
        try:
            is_fully_filled = self.strategy.on_order_filled(order_id, fill_info)

            if is_fully_filled:
                # 完全成交：立即处理
                self._cancel_delayed_update(order_id)
                lg.info(f"✅ 订单完全成交，策略状态已更新")
                self._print_position_status()
            else:
                # 部分成交：延迟处理
                lg.info(f"🔶 订单部分成交，等待剩余数量成交")
                self._schedule_delayed_update(order_id)

        except Exception as e:
            lg.error(f"❌ 策略状态更新异常: {e}")

    def _schedule_delayed_update(self, order_id: str):
        """调度延迟更新"""
        with self.timer_lock:
            # 取消之前的定时器
            if order_id in self.delayed_timers:
                self.delayed_timers[order_id].cancel()

            # 设置新的延迟处理定时器
            self.delayed_timers[order_id] = threading.Timer(
                self.PARTIAL_FILL_DELAY,
                self._handle_partial_fill_timeout,
                [order_id]
            )
            self.delayed_timers[order_id].start()

    def _cancel_delayed_update(self, order_id: str):
        """取消延迟更新"""
        with self.timer_lock:
            if order_id in self.delayed_timers:
                self.delayed_timers[order_id].cancel()
                del self.delayed_timers[order_id]

    def _handle_partial_fill_timeout(self, order_id: str):
        """处理部分成交延迟超时"""
        with self.timer_lock:
            if order_id in self.delayed_timers:
                del self.delayed_timers[order_id]

        try:
            # 检查订单在超时期间是否完全成交
            lg.info(f"🔶 部分成交延迟处理完成，网格状态已调整")

        except Exception as e:
            lg.error(f"❌ 部分成交延迟处理异常: {e}")

    def on_order_status_changed(self, status_info: dict):
        """处理订单状态变化回报"""
        order_id = status_info['order_id']
        order_status = status_info['order_status']

        # 检查是否有成交信息
        executed_qty = status_info.get('executed_quantity', 0)
        executed_price = status_info.get('executed_price', 0.0)

        # 如果有成交，先处理成交回报
        if executed_qty > 0:
            fill_info = {
                'order_id': order_id,
                'action': 'SELL' if status_info.get('side') == 'Sell' else 'BUY',
                'qty': executed_qty,
                'price': executed_price
            }
            self.on_order_filled(fill_info)

        lg.info(f"📋 订单状态变化 | 订单ID: {order_id}")
        lg.info(f"   状态: {order_status}")

        # 获取订单的成交信息
        dealt_qty = status_info.get('dealt_qty', 0)
        total_qty = status_info.get('qty', 0)

        if dealt_qty > 0:
            lg.info(f"   成交进度: {dealt_qty}/{total_qty} 股")

        # 处理订单撤销状态
        if order_status in ['CANCELLED_ALL', 'CANCELLED_PART']:
            try:
                self.strategy.on_order_cancelled(order_id, cancelled_type=order_status)
                lg.info(f"✅ 订单撤销处理完成")
                self._print_grid_status()
            except Exception as e:
                lg.error(f"❌ 订单撤销处理异常: {e}")

        # 处理其他订单状态
        elif order_status in ['FAILED', 'DISABLED']:
            lg.warning(f"⚠️ 订单异常状态: {order_status}")
            try:
                self.strategy.on_order_cancelled(order_id)
                lg.info(f"✅ 异常订单清理完成")
            except Exception as e:
                lg.error(f"❌ 异常订单清理失败: {e}")

        # 记录其他状态变化
        elif order_status in ['SUBMITTED', 'WAITING_SUBMIT', 'SUBMITTING']:
            lg.info(f"📤 订单处理中: {order_status}")
        elif order_status in ['FILLED_PART']:
            lg.info(f"🔶 订单部分成交: {order_status}")
        elif order_status in ['FILLED_ALL']:
            lg.info(f"✅ 订单完全成交: {order_status}")

    def _print_grid_status(self):
        """打印网格状态"""
        status = self.strategy.get_grid_status()
        lg.info(f"基准价: {status['base_price']:.2f}")

        # 显示活跃网格层级
        waiting_sell = []
        waiting_buy = []
        pending_orders = []

        for level, state in status['grid_state'].items():
            if state['status'] == 'waiting_sell':
                waiting_sell.append(f"L{level}@{state['sell_price']:.2f}")
            elif state['status'] == 'waiting_buy':
                waiting_buy.append(f"L{level}@{state['buy_price']:.2f}")
            elif state['status'] in ['pending_sell', 'pending_buy']:
                pending_orders.append(f"L{level}({state['status']})")

        if waiting_sell:
            lg.info(f"等待卖空: {', '.join(waiting_sell[:5])}{'...' if len(waiting_sell) > 5 else ''}")
        if waiting_buy:
            lg.info(f"等待买回: {', '.join(waiting_buy[:5])}{'...' if len(waiting_buy) > 5 else ''}")
        if pending_orders:
            lg.info(f"待成交: {', '.join(pending_orders)}")

    def _print_position_status(self):
        """打印持仓状态"""
        status = self.strategy.get_grid_status()
        lg.info(f"📊 当前持仓: {status['current_position']}")

    def get_system_status(self) -> dict:
        """获取系统整体状态"""
        grid_status = self.strategy.get_grid_status()
        return {
            'symbol': self.symbol,
            'current_date': self.today,
            'grid_status': grid_status
        }

    def force_sync_position(self):
        """强制同步持仓"""
        try:
            position_df = self.trader.query_position()
            if position_df is not None and not position_df.empty:
                target_position = position_df[position_df['symbol'] == self.symbol]
                if not target_position.empty:
                    real_qty = int(target_position.iloc[0]['quantity'])
                    strategy_qty = self.strategy.current_position

                    if real_qty != strategy_qty:
                        lg.info(f"🔄 持仓不同步 - 策略: {strategy_qty}, 实际: {real_qty}")
                        self.strategy.reset_position(real_qty)
                        lg.info(f"✅ 持仓已同步至: {real_qty}")
                    else:
                        lg.info(f"✅ 持仓已同步: {real_qty}")
                else:
                    lg.info(f"📊 {self.symbol} 无持仓")
                    self.strategy.reset_position(0)
            else:
                lg.info("📊 查询持仓为空")
                self.strategy.reset_position(0)

            # 同步后清除可能的待成交订单状态
            self.strategy.force_clear_pending_orders()

        except Exception as e:
            lg.error(f"❌ 持仓同步异常: {e}")

    def stop(self):
        """停止交易系统"""
        lg.info("🛑 停止交易系统")
        try:
            # 显示最终状态
            lg.info("📊 最终系统状态:")
            status = self.get_system_status()
            lg.info(f"   持仓: {status['grid_status']['current_position']}")
            lg.info(f"   待成交订单: {status['grid_status']['pending_orders_count']} 个")

            # 停止数据流
            self.data_feed.stop_realtime_quote(self.symbol)

            # 关闭交易连接
            self.trader.close()

            lg.info("✅ 系统已安全关闭")
        except Exception as e:
            lg.error(f"❌ 系统关闭异常: {e}")


# ===================== 启动入口 =====================

def main():
    """主函数"""
    # 配置参数
    SYMBOL = "SQQQ.US"  # 修正股票代码格式
    CSV_FILE = "SQQQ.csv"

    # 长桥API配置 - 请替换为你的真实配置
    APP_KEY = "你的APP_KEY"
    APP_SECRET = "你的APP_SECRET"
    ACCESS_TOKEN = "你的ACCESS_TOKEN"

    # 检查配置
    if APP_KEY == "你的APP_KEY":
        lg.error("请先配置长桥API密钥!")
        return

    bot = MainController(SYMBOL, CSV_FILE, APP_KEY, APP_SECRET, ACCESS_TOKEN)
    try:
        # 启动前先同步一次持仓
        lg.info("🔄 启动前持仓同步...")
        bot.force_sync_position()

        # 启动系统
        bot.start()
    except KeyboardInterrupt:
        lg.info("📚 用户中断退出")
    except Exception as e:
        lg.error(f"❌ 系统异常: {e}")
        import traceback
        lg.error(traceback.format_exc())
    finally:
        bot.stop()


if __name__ == "__main__":
    main()