{"cells": [{"cell_type": "markdown", "id": "338ffd3a", "metadata": {}, "source": ["# 数据接口"]}, {"cell_type": "code", "execution_count": 2, "id": "29263f9b", "metadata": {"ExecuteTime": {"end_time": "2025-08-29T16:25:21.812235Z", "start_time": "2025-08-29T16:25:21.339231Z"}}, "outputs": [], "source": ["from futu import *\n", "import pandas as pd\n", "import queue, threading\n", "from typing import Callable, Optional\n", "from datetime import datetime, time, date\n", "import pytz\n", "import logging as lg\n", "\n", "# 定义日志文件名，按日期区分\n", "log_filename = f'grid_trading_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log'\n", "\n", "# 配置日志记录\n", "lg.basicConfig(\n", "    level=lg.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    handlers=[\n", "        lg.<PERSON>H<PERSON>(log_filename, encoding='utf-8'),\n", "        lg.<PERSON><PERSON><PERSON><PERSON>()\n", "    ]\n", ")\n", "\n", "class StackQuote(StockQuoteHandlerBase):\n", "    \"\"\"实时行情回调处理器（支持按美东时间获取真实价格）\"\"\"\n", "    \n", "    def __init__(self, callback):\n", "        super().__init__()\n", "        self.callback = callback\n", "\n", "    @staticmethod\n", "    def get_us_trading_session():\n", "        \"\"\"获取当前美东交易阶段：pre / regular / after / overnight\"\"\"\n", "        eastern = pytz.timezone('US/Eastern')\n", "        now = datetime.now(eastern).time()\n", "\n", "        if time(4, 0) <= now < time(9, 30):\n", "            return 'pre'\n", "        elif time(9, 30) <= now < time(16, 0):\n", "            return 'regular'\n", "        elif time(16, 0) <= now < time(20, 0):\n", "            return 'after'\n", "        else:\n", "            return 'overnight'\n", "\n", "    @classmethod\n", "    def extract_real_price(cls, row):\n", "        \"\"\"根据交易时段提取当前应使用的价格字段\"\"\"\n", "        session = cls.get_us_trading_session()\n", "\n", "        if session == 'pre':\n", "            return row.get('pre_price', None), '盘前'\n", "        elif session == 'regular':\n", "            return row.get('last_price', None), '盘中'\n", "        elif session == 'after':\n", "            return row.get('after_price', None), '盘后'\n", "        else:\n", "            return row.get('overnight_price', None), '隔夜'\n", "\n", "    def on_recv_rsp(self, rsp_pb):\n", "        ret_code, data = super().on_recv_rsp(rsp_pb)\n", "        if ret_code != RET_OK or data.empty:\n", "            return ret_code, data\n", "\n", "        try:\n", "            row = data.iloc[0]\n", "            price, stage = self.extract_real_price(row)\n", "\n", "            if price is None or pd.isna(price):\n", "                lg.warning(f\"获取空价格数据：{row.to_dict()}\")\n", "                return RET_OK, data\n", "\n", "            tick = {\n", "                'code': row['code'],\n", "                'price': price,\n", "                'stage': stage,\n", "                'data_time': datetime.now(pytz.timezone('US/Eastern')).time()\n", "            }\n", "            self.callback(tick)\n", "        except Exception as e:\n", "            lg.info(f\"实时数据处理异常: {e}\")\n", "            return RET_ERROR, str(e)\n", "\n", "        return RET_OK, data\n", "\n", "\n", "class DataFeed:\n", "    \"\"\"统一数据接口模块\"\"\"\n", "    def __init__(self, host='127.0.0.1', port=11111):\n", "        self.quote_ctx = OpenQuoteContext(host=host, port=port)\n", "        self._realtime_thread = None\n", "        self._running = False\n", "        self._data_queue = queue.Queue()\n", "\n", "    def __del__(self):\n", "        try:\n", "            self.quote_ctx.close()\n", "        except:\n", "            pass\n", "\n", "    def get_daily_data(self, symbol, start, end):\n", "        \"\"\"获取历史日线数据\"\"\"\n", "        ret, data, _ = self.quote_ctx.request_history_kline(\n", "            symbol, start=start, end=end, ktype=KLType.K_DAY)\n", "        if ret != RET_OK:\n", "            raise RuntimeError(f\"历史K线获取失败：{data}\")\n", "        \n", "        if data.empty:\n", "            lg.warning(f\"[警告] {symbol} 在 {start}~{end} 无数据\")\n", "            return pd.DataFrame()\n", "\n", "        # 保存到CSV文件\n", "        data.to_csv(f'{symbol}.csv', index=False)\n", "        lg.info(f\"历史数据已更新！\")\n", "        return data\n", "\n", "    def start_realtime_quote(self, symbol, on_tick):\n", "        \"\"\"\n", "        订阅并启动实时行情流监听（非阻塞）\n", "        参数 on_tick: 用户自定义的行情回调函数，接收tick dict\n", "        \"\"\"\n", "        handler = StackQuote(on_tick)\n", "        self.quote_ctx.set_handler(handler)\n", "\n", "        ret, err = self.quote_ctx.subscribe(symbol, [SubType.QUOTE])\n", "        if ret != RET_OK:\n", "            raise RuntimeError(f\"订阅失败: {err}\")\n", "        \n", "        self._running = True\n", "\n", "        def listen_loop():\n", "            lg.info(f\"开始监听 {symbol} 的实时行情...\")\n", "            while self._running:\n", "                time.sleep(0.5)  # 防止CPU打满\n", "\n", "        self._realtime_thread = threading.Thread(target=listen_loop, daemon=True)\n", "        self._realtime_thread.start()\n", "\n", "    def stop_realtime_quote(self, symbol):\n", "        \"\"\"手动停止实时行情监听\"\"\"\n", "        self._running = False\n", "        try:\n", "            self.quote_ctx.unsubscribe(symbol, [SubType.QUOTE])\n", "        except:\n", "            pass\n", "\n", "    def get_stock_quote(self, symbol: str, on_tick_callback: Optional[Callable[[dict], None]] = None):\n", "        \"\"\"实时驱动系统接口 - 支持回调模式的行情推送\"\"\"\n", "        # 定义数据入队回调\n", "        def queue_handler(tick_data):\n", "            self._data_queue.put(tick_data)\n", "        \n", "        # 订阅实时行情，数据推送到队列\n", "        handler = StackQuote(queue_handler)\n", "        self.quote_ctx.set_handler(handler)\n", "        \n", "        ret, err = self.quote_ctx.subscribe(symbol, [SubType.QUOTE])\n", "        if ret != RET_OK:\n", "            raise RuntimeError(f\"订阅失败: {err}\")\n", "        \n", "        self._running = True\n", "        lg.info(f\"🚀 开始实时驱动 {symbol} 行情...\")\n", "        \n", "        # 实时数据处理循环\n", "        while self._running:\n", "            try:\n", "                # 从队列获取行情数据（1秒超时）\n", "                data = self._data_queue.get(timeout=1)\n", "                \n", "                # 推送给外部回调处理器\n", "                if on_tick_callback:\n", "                    on_tick_callback(data)  # 实时将数据推送出去\n", "                else:\n", "                    # 默认行为：打印行情数据\n", "                    lg.info(f\"📈 收到行情数据: {data}\")\n", "                    \n", "            except queue.Empty:\n", "                # 队列超时，继续循环\n", "                continue\n", "            except KeyboardInterrupt:\n", "                lg.info(\"⏹️  用户中断，停止行情监听...\")\n", "                break\n", "            except Exception as e:\n", "                lg.error(f\"❌ 行情处理异常: {e}\")\n", "                continue\n", "        \n", "        # 清理资源\n", "        self.stop_realtime_quote(symbol)\n"]}, {"cell_type": "markdown", "id": "3c9724d5", "metadata": {}, "source": ["# 交易接口（贝塔版）"]}, {"cell_type": "code", "execution_count": null, "id": "44ffaff6", "metadata": {"ExecuteTime": {"end_time": "2025-08-29T16:25:21.874791Z", "start_time": "2025-08-29T16:25:21.845395Z"}}, "outputs": [], "source": ["from longport.openapi import TradeContext, Config, OrderType, OrderSide, TimeInForceType, PushOrderChanged, TopicType\n", "from decimal import Decimal\n", "import logging as lg\n", "import pandas as pd\n", "from typing import Callable, Optional\n", "\n", "class OrderstatusHandler:\n", "    \"\"\"订单状态变化处理器\"\"\"\n", "    def __init__(self, callback: Callable):\n", "        self.callback = callback\n", "\n", "    def handle_order_changed(self, event: PushOrderChanged):\n", "        \"\"\"处理订单状态变化推送\"\"\"\n", "        try:\n", "            order_status_info = {\n", "                'order_id': event.order_id,\n", "                'code': event.symbol,\n", "                'order_status': event.status,\n", "                'order_type': str(event.order_type),\n", "                'qty': int(event.executed_quantity) if event.executed_quantity else 0,\n", "                'executed_price': float(event.executed_price) if event.executed_price else 0.0\n", "            }\n", "\n", "            lg.info(f\"订单状态更新 - ID: {order_status_info['order_id']}, 状态:{order_status_info['order_status']}\")\n", "            lg.info(f\"{order_status_info}\")\n", "            if self.callback:\n", "                self.callback(order_status_info)\n", "        \n", "        except Exception as e:\n", "            lg.error(f\"订单状态回调函数执行异常：{e}\")\n", "\n", "class Trader:\n", "    \"\"\"长桥交易接口\"\"\"\n", "    def __init__(self, app_key: str=\"\",\n", "                 app_secret: str=\",\n", "                 access_token: str=\"\"):\n", "        \"\"\"初始化长桥交易连接\"\"\"\n", "        self.config = Config(app_key=app_key, app_secret=app_secret, access_token=access_token)\n", "        self.trd_ctx = TradeContext(self.config)\n", "        self.order_status_handler = None\n", "        self._is_subscribed = False\n", "    \n", "    def query_position(self) -> Optional[pd.DataFrame]:\n", "        \"\"\"查询持仓信息\"\"\"\n", "        try:\n", "            resp = self.trd_ctx.stock_positions()\n", "            if resp and resp.channels:\n", "                positions_data = []\n", "                for channel in resp.channels:\n", "                    for position in channel.positions:\n", "                        positions_data.append({\n", "                            'symbol': position.symbol,\n", "                            'symbol_name': position.symbol_name,\n", "                            'quantity': int(position.quantity),\n", "                            'available_quantity': int(position.available_quantity)\n", "                        })\n", "                return pd.DataFrame(positions_data) if positions_data else pd.DataFrame()\n", "            else:\n", "                lg.info('持仓查询成功: 无持仓')\n", "                return pd.DataFrame()\n", "        except Exception as e:\n", "            lg.error(f'持仓查询异常: {e}')\n", "            return None\n", "        \n", "    def place_order(self, symbol: str, side: str, quantity: int, price: float) -> Optional[str]:\n", "        \"\"\"\n", "        下单\n", "        Args:\n", "            symbol: 股票代码 (如 \"AAPL.US\")\n", "            side: 买卖方向 (\"Buy\" 或 \"Sell\")\n", "            quantity: 数量\n", "            price: 价格\n", "        Returns:\n", "            订单ID或None\n", "        \"\"\"\n", "        try:\n", "            # 转换参数\n", "            order_side = OrderSide.Buy if side == \"Buy\" else OrderSide.Sell\n", "            \n", "            resp = self.trd_ctx.submit_order(\n", "                symbol='SQQQ.US',\n", "                order_type=OrderType.LO,\n", "                side=order_side,\n", "                submitted_quantity=Decimal(quantity),\n", "                time_in_force=TimeInForceType.Day,\n", "                submitted_price=Decimal(price)\n", "            )\n", "\n", "            if resp and resp.order_id:\n", "                lg.info(f'下单成功：{resp.order_id}')\n", "                return resp.order_id\n", "            else:\n", "                lg.error('下单失败：无订单ID返回')\n", "                return None\n", "        except Exception as e:\n", "            lg.error(f'下单异常：{e}')\n", "            return None\n", "        \n", "    def set_order_status_handler(self, callback: Callable) -> bool:\n", "        \"\"\"设置订单状态变化处理器\"\"\"\n", "        try:\n", "            self.order_status_handler = OrderstatusHandler(callback)\n", "\n", "            # 设置订单状态变化回调\n", "            self.trd_ctx.set_on_order_changed(self.order_status_handler.handle_order_changed)\n", "\n", "            # 订阅私有频道\n", "            if not self._is_subscribed:\n", "                self.trd_ctx.subscribe([TopicType.Private])\n", "                self._is_subscribed = True\n", "                lg.info(\"订单状态变化监听器注册成功\")\n", "                return True\n", "            \n", "        except Exception as e:\n", "            lg.error(f\"订单状态变化监听器注册失败：{e}\")\n", "            return False\n", "    \n", "    def remove_order_status_handler(self) -> bool:\n", "        \"\"\"移除订单状态变化处理器\"\"\"\n", "        try:\n", "            if self.order_status_handler:\n", "                self.trd_ctx.unsubscribe([TopicType.Private])\n", "                self._is_subscribed = False\n", "\n", "            self.order_status_handler = None\n", "            lg.info(\"订单状态变化监听器已移除\")\n", "            return True\n", "        except Exception as e:\n", "            lg.error(f\"订单状态变化监听器移除失败: {e}\")\n", "            return False\n", "        \n", "    def close(self):\n", "        \"\"\"关闭交易连接\"\"\"\n", "        try:\n", "            # 移除事件处理器\n", "            self.remove_order_status_handler()\n", "            lg.info(\"长桥交易连接已关闭\")\n", "        except Exception as e:\n", "            lg.error(f\"关闭连接异常: {e}\")\n", "\n"]}, {"cell_type": "markdown", "id": "0faa61f3", "metadata": {}, "source": ["# 主控模块"]}, {"cell_type": "code", "execution_count": null, "id": "60be7966", "metadata": {"ExecuteTime": {"start_time": "2025-08-29T16:39:34.464315Z"}, "jupyter": {"is_executing": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[0;30m2025-08-30 00:39:34,535 | 48828 | [open_context_base.py] _send_init_connect_sync:311: InitConnect ok: conn_id=2, host=127.0.0.1, port=11111, user_id=12231909\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-30 00:39:34,565 - INFO - 历史数据已更新！\n", "2025-08-30 00:39:34,856 - INFO - 网格状态已经加载到grid_state.json\n", "2025-08-30 00:39:34,858 - INFO - ✅ 从历史状态文件加载网格配置\n", "2025-08-30 00:39:34,871 - INFO - 订单状态变化监听器注册成功\n", "2025-08-30 00:39:34,874 - INFO - 📊 网格策略初始状态:\n", "2025-08-30 00:39:34,875 - INFO - 基准价: 16.71\n", "2025-08-30 00:39:34,876 - INFO - 等待买回: L1@16.79, L2@17.04, L3@17.28, L4@17.53\n", "2025-08-30 00:39:34,878 - INFO - 🔄 启动前持仓同步...\n", "2025-08-30 00:39:35,052 - INFO - 📊 US.SQQQ 无持仓\n", "2025-08-30 00:39:35,053 - INFO - 持仓重置为: 0\n", "2025-08-30 00:39:35,054 - INFO - 强制清除了0个待成交订单状态和成交跟踪信息\n", "2025-08-30 00:39:35,055 - INFO - 🚀 启动主交易控制器，标的: US.SQQQ\n", "2025-08-30 00:39:35,073 - INFO - 🚀 开始实时驱动 US.SQQQ 行情...\n", "2025-08-30 00:39:35,074 - INFO - 📈 实时价格: 17.9413 (盘中) @ 12:39:35.073327\n", "2025-08-30 00:39:35,075 - INFO - 🟡 当前无交易信号\n"]}], "source": ["import pandas as pd\n", "from datetime import datetime, timedelta, date\n", "from typing import List, Dict, Any, Optional\n", "from longport.openapi import OrderSide, OrderStatus\n", "import json\n", "import os\n", "\n", "# 统一的交易动作定义\n", "class TradeAction:\n", "    SELL = 'SELL'\n", "    BUY = 'BUY'\n", "\n", "class IntegratedGridStrategy:\n", "    \"\"\"整合的网格交易策略类，包含完整的状态管理和订单控制\"\"\"\n", "\n", "    def __init__(self, csv_file: str, grid_levels: int = 4, step_pct: float = 0.015, layer_offset: float = 0.005,\n", "                 qty_per_order: int = 20, cover_discount: float = 0.985, lookback_days: int = 90):\n", "        \"\"\"\n", "        初始化整合的网格策略\n", "        \"\"\"\n", "        self.csv_file = csv_file\n", "        self.grid_levels = grid_levels\n", "        self.step_pct = step_pct\n", "        self.layer_offset = layer_offset\n", "        self.qty_per_order = qty_per_order\n", "        self.cover_discount = cover_discount\n", "        self.lookback_days = lookback_days\n", "\n", "        self.csv_data = self._load_csv_data()     # 加载CSV数据\n", "        self.grid_state: Dict[int, Dict[str, Any]] = {}# 网格状态字典\n", "        self.base_price_L = 0.0                   # 当前基准价（历史低点）\n", "        self.current_position = 0                 # 当前持仓数量（负数表示空头持仓）\n", "        self.last_grid_update_date = None         # 最后更新网格的日期\n", "        self.order_to_level: Dict[str, int] = {}  # 订单ID到层级的映射（用于成交回报处理）\n", "        self.order_fill_tracker: Dict[str, Dict[str, Any]] = {}# 订单成交跟踪器\n", "        self.last_price = None                    # 最后处理的价格（避免重复处理相同价格）\n", "\n", "    def _load_csv_data(self) -> pd.DataFrame:\n", "        \"\"\"加载CSV数据并预处理\"\"\"\n", "        try:\n", "            df = pd.read_csv(self.csv_file)\n", "            df['time_key'] = pd.to_datetime(df['time_key'])\n", "            df['date'] = df['time_key'].dt.date\n", "            return df\n", "        except Exception as e:\n", "            raise Exception(f\"加载CSV文件失败: {e}\")\n", "\n", "    def get_historical_low(self, target_date) -> float:\n", "        \"\"\"在指定日期之前，回看 lookback_days 天的最低价\"\"\"\n", "        self.csv_data['low'] = pd.to_numeric(self.csv_data['low'], errors='coerce')\n", "        df_filtered = self.csv_data[self.csv_data['date'] < target_date]\n", "        if df_filtered.empty:\n", "            return 0.0\n", "\n", "        daily_lows = df_filtered.groupby('date')['low'].min()\n", "        recent_lows = daily_lows.tail(self.lookback_days).dropna()\n", "        return recent_lows.min()\n", "\n", "    def initialize_grid(self, today_date):\n", "        \"\"\"程序启动时初始化网格\"\"\"\n", "        self.base_price_L = self.get_historical_low(today_date)\n", "        if self.grid_state:\n", "            lg.info(\"网格状态已存在，跳过初始化\")\n", "            return\n", "        if os.path.exists('grid_state.json'):\n", "            self.load_grid_state('grid_state.json')\n", "            lg.info(\"✅ 从历史状态文件加载网格配置\")\n", "        else:\n", "            self.base_price_L = self.get_historical_low(today_date)\n", "            if self.base_price_L <= 0:\n", "                lg.warning(f\"⚠️ 无法获取有效的历史低点，日期: {today_date}\")\n", "                return\n", "\n", "            lg.info(f\"✅ 初始化网格，基准价: {self.base_price_L:.2f}\")\n", "            for n in range(1, self.grid_levels + 1):\n", "                sell_price = self.base_price_L * (1 + self.layer_offset + n * self.step_pct)\n", "                buy_price = sell_price * self.cover_discount\n", "                self.grid_state[n] = {\n", "                    'sell_price': round(sell_price, 2),\n", "                    'buy_price': round(buy_price, 2),\n", "                    'status': 'waiting_sell',\n", "                    'position_qty': 0,\n", "                    'order_id': None,\n", "                    'last_signal_price': None\n", "                }\n", "            self.save_grid_state('grid_state.json')\n", "\n", "    def _change_level_position(self, level: int, delta_qty: int) -> None:\n", "        \"\"\"更新层级持仓数量\"\"\"\n", "        if level not in self.grid_state:\n", "            lg.warning(f\"警告: 未找到层级{level}的网格状态\")\n", "            return\n", "        state = self.grid_state[level]\n", "        state['position_qty'] = int(state.get('position_qty', 0)) + int(delta_qty)\n", "\n", "    def update_grid(self, today_date) -> None:\n", "        \"\"\"每日刷新网格价位\"\"\"\n", "        if self.last_grid_update_date == today_date:\n", "            return\n", "\n", "        self.base_price_L = float(self.get_historical_low(today_date))\n", "        lg.info(f\"self.base_price_L的值为{self.base_price_L},类型为{type(self.base_price_L)}\")\n", "\n", "        lg.info(\"🚀 开始刷新网格\")\n", "        for n, state in self.grid_state.items():\n", "            sell_price = self.base_price_L * (1 + self.layer_offset + n * self.step_pct)\n", "            buy_price = sell_price * self.cover_discount\n", "            state['sell_price'] = round(sell_price, 2)\n", "            state['buy_price'] = round(buy_price, 2)\n", "\n", "        self.last_grid_update_date = today_date\n", "        lg.info(f\"网格更新完成，当前持仓: {self.current_position}\")\n", "\n", "        self.save_grid_state('grid_state.json')\n", "\n", "    def check_sell_signals(self, current_price: float) -> List[Dict[str, Any]]:\n", "        \"\"\"检查是否满足卖出信号条件\"\"\"\n", "        signals = []\n", "\n", "        for level, state in self.grid_state.items():\n", "            # 只有在明确等待卖出状态且无待成交订单时才发出信号\n", "            if (state['status'] == 'waiting_sell' and\n", "                state['order_id'] is None and\n", "                current_price >= state['sell_price']):\n", "\n", "                # 防止重复信号：检查是否已经在相同价格处理过\n", "                if state['last_signal_price'] != current_price:\n", "                    signals.append({\n", "                        'action': TradeAction.SELL,\n", "                        'price': state['sell_price'],\n", "                        'qty': self.qty_per_order,\n", "                        'level': level\n", "                    })\n", "\n", "        return signals\n", "\n", "    def check_buy_signals(self, current_price: float) -> List[Dict[str, Any]]:\n", "        \"\"\"检查是否满足买入信号条件\"\"\"\n", "        signals = []\n", "\n", "        for level, state in self.grid_state.items():\n", "            # 只有在明确等待买入状态且无待成交订单时才发出信号\n", "            if (state['status'] == 'waiting_buy' and\n", "                state['order_id'] is None and\n", "                current_price <= state['buy_price']):\n", "\n", "                # 防止重复信号：检查是否已经在相同价格处理过\n", "                if state['last_signal_price'] != current_price:\n", "                    signals.append({\n", "                        'action': TradeAction.BUY,\n", "                        'price': state['buy_price'],\n", "                        'qty': abs(state['position_qty']),\n", "                        'level': level\n", "                    })\n", "\n", "        return signals\n", "\n", "    def on_price(self, current_price: float) -> List[Dict[str, Any]]:\n", "        \"\"\"根据当前价格判断是否触发交易信号\"\"\"\n", "        # 避免重复处理相同价格\n", "        if self.last_price == current_price:\n", "            return []\n", "\n", "        if not self.grid_state:\n", "            return []\n", "\n", "        # 整合买入和卖出信号\n", "        sell_signals = self.check_sell_signals(current_price)\n", "        buy_signals = self.check_buy_signals(current_price)\n", "\n", "        # 更新最后处理价格\n", "        self.last_price = current_price\n", "\n", "        return sell_signals + buy_signals\n", "\n", "    def on_signal_processed(self, signal: Dict[str, Any], order_id: str) -> None:\n", "        \"\"\"信号处理完成回调，更新层级状态\"\"\"\n", "        level = signal['level']\n", "        if level not in self.grid_state:\n", "            return\n", "\n", "        state = self.grid_state[level]\n", "\n", "        if order_id:\n", "            # 下单成功\n", "            if signal['action'] == TradeAction.SELL:\n", "                state['status'] = 'pending_sell'\n", "            elif signal['action'] == TradeAction.BUY:\n", "                state['status'] = 'pending_buy'\n", "\n", "            state['order_id'] = order_id\n", "            state['last_signal_price'] = signal['price']\n", "            self.order_to_level[order_id] = level\n", "\n", "            # 初始化订单成交跟踪器\n", "            self.order_fill_tracker[order_id] = {\n", "                'total_qty': signal['qty'],\n", "                'filled_qty': 0,\n", "                'level': level,\n", "                'action': signal['action'],\n", "                'avg_price': 0.0,\n", "                'fill_count': 0\n", "            }\n", "\n", "            self.save_grid_state('grid_state.json')\n", "\n", "            lg.info(f\"层级{level}状态更新: {state['status']}, 订单ID: {order_id}\")\n", "        else:\n", "            # 下单失败，清除信号价格记录\n", "            state['last_signal_price'] = None\n", "            lg.error(f\"层级{level}下单失败，状态保持: {state['status']}\")\n", "\n", "    def on_order_filled(self, order_id: str, fill_info: Dict[str, Any]) -> bool:\n", "        \"\"\"\n", "        处理订单成交\n", "        Args:\n", "            order_id: 订单ID\n", "            fill_info: 成交信息 {'action', 'qty', 'price'}\n", "        Returns:\n", "            总是返回True表示处理完成\n", "        \"\"\"\n", "        if order_id not in self.order_to_level:\n", "            lg.warning(f\"警告: 未找到订单{order_id}对应的网格层级\")\n", "            return False\n", "\n", "        level = self.order_to_level[order_id]\n", "        if level not in self.grid_state:\n", "            lg.warning(f\"警告: 未找到层级{level}的网格状态\")\n", "            return False\n", "\n", "        state = self.grid_state[level]\n", "        action = fill_info['action']\n", "        fill_qty = int(fill_info['qty'])\n", "        fill_price = float(fill_info['price'])\n", "\n", "        lg.info(f\"处理层级{level}订单成交: {action} {fill_qty}股 @ {fill_price:.2f}\")\n", "\n", "        # 处理成交\n", "        if action == 'SELL' and state['status'] == 'pending_sell':\n", "            # 卖空成交\n", "            self.current_position -= fill_qty\n", "            self._change_level_position(level, -fill_qty)\n", "            state['status'] = 'waiting_buy'\n", "            lg.info(f\"层级{level}卖空成交，数量: {fill_qty}, 价格: {fill_price:.2f}\")\n", "\n", "        elif action == 'BUY' and state['status'] == 'pending_buy':\n", "            # 买回成交\n", "            self.current_position += fill_qty\n", "            self._change_level_position(level, fill_qty)\n", "            state['status'] = 'waiting_sell'\n", "            lg.info(f\"层级{level}买回成交，数量: {fill_qty}, 价格: {fill_price:.2f}\")\n", "\n", "        # 清理订单状态\n", "        state['order_id'] = None\n", "        state['last_signal_price'] = None\n", "        del self.order_to_level[order_id]\n", "\n", "        lg.info(f\"当前总持仓: {self.current_position}\")\n", "        self.save_grid_state('grid_state.json')\n", "        return True\n", "\n", "    '''def on_order_cancelled(self, order_id: str, cancelled_type: str) -> None:\n", "        \"\"\"\n", "        处理订单撤销\n", "        Args:\n", "            order_id: 订单ID\n", "            cancelled_type: 撤销类型，根据LongPort API实际返回值判断\n", "        \"\"\"\n", "        if order_id not in self.order_to_level:\n", "            lg.warning(f\"警告: 未找到订单{order_id}对应的网格层级\")\n", "            return\n", "\n", "        level = self.order_to_level[order_id]\n", "        if level not in self.grid_state:\n", "            lg.warning(f\"警告: 未找到层级{level}的网格状态\")\n", "            return\n", "\n", "        state = self.grid_state[level]\n", "        lg.info(f\"处理订单撤销: order_id={order_id}, type={cancelled_type}, 层级={level}\")\n", "\n", "        # 撤单处理：只考虑完全撤单，恢复到等待状态\n", "        if state['status'] == 'pending_sell':\n", "            # 卖出订单被撤销，恢复到等待卖出\n", "            state['status'] = 'waiting_sell'\n", "            lg.info(f\"层级{level}卖出订单撤销，恢复到waiting_sell\")\n", "\n", "        elif state['status'] == 'pending_buy':\n", "            # 买入订单被撤销，根据持仓情况恢复状态\n", "            level_position = state.get('position_qty', 0)\n", "            if level_position < 0:\n", "                # 该层级有空头持仓，继续等待买回\n", "                state['status'] = 'waiting_buy'\n", "                lg.info(f\"层级{level}买入订单撤销，层级持仓{level_position}，继续waiting_buy\")\n", "            else:\n", "                # 该层级无空头持仓，可以重新卖出\n", "                state['status'] = 'waiting_sell'\n", "                lg.info(f\"层级{level}买入订单撤销，层级持仓{level_position}，恢复waiting_sell\")\n", "\n", "        # 清理订单状态\n", "        state['order_id'] = None\n", "        state['last_signal_price'] = None\n", "        del self.order_to_level[order_id]\n", "\n", "        self.save_grid_state('grid_state.json')\n", "        lg.info(f\"层级{level}撤单处理完成，最终状态: {state['status']}\")'''\n", "\n", "    def get_grid_status(self) -> Dict[str, Any]:\n", "        \"\"\"获取当前网格状态信息\"\"\"\n", "        return {\n", "            'base_price': self.base_price_L,\n", "            'current_position': self.current_position,\n", "            'grid_state': self.grid_state.copy(),\n", "            'last_update_date': self.last_grid_update_date,\n", "            'pending_orders_count': len(self.order_to_level),\n", "            'pending_orders': self.order_to_level.copy()\n", "        }\n", "\n", "    def reset_position(self, new_position: int = 0) -> None:\n", "        \"\"\"重置持仓数量\"\"\"\n", "        self.current_position = new_position\n", "        lg.info(f\"持仓重置为: {self.current_position}\")\n", "\n", "    def force_clear_pending_orders(self) -> None:\n", "        \"\"\"强制清除所有待成交订单状态\"\"\"\n", "        cleared_count = 0\n", "        for level, state in self.grid_state.items():\n", "            if state['status'] in ['pending_sell', 'pending_buy']:\n", "                # 根据是否已卖出恢复到正确状态\n", "                if state.get('position_qty', 0):\n", "                    state['status'] = 'waiting_sell'\n", "                else:\n", "                    state['status'] = 'waiting_buy'\n", "\n", "                state['order_id'] = None\n", "                state['last_signal_price'] = None\n", "                cleared_count += 1\n", "\n", "        self.order_to_level.clear()\n", "        self.order_fill_tracker.clear()  # 清除成交跟踪器\n", "        lg.info(f\"强制清除了{cleared_count}个待成交订单状态和成交跟踪信息\")\n", "\n", "    def save_grid_state(self, file_path:str):\n", "        \"\"\"保存网格状态\"\"\"\n", "        try:\n", "            with open(file_path, 'w') as f:\n", "                json.dump({str(k): v for k, v in self.grid_state.items()}, f, indent=4)\n", "            lg.info(f\"网格状态已经保存到{file_path}\")\n", "        except Exception as e:\n", "            lg.error(f\"保存网格状态失败: {e}\")\n", "\n", "    def load_grid_state(self, file_path:str):\n", "        \"\"\"加载网格状态\"\"\"\n", "        if  not os.path.exists('grid_state.json'):\n", "            lg.info(f\"网格状态文件不存在: {file_path}\")\n", "            return\n", "\n", "        try:\n", "            with open(file_path, 'r') as f:\n", "                data = json.load(f)\n", "            self.grid_state = {int(k): v for k, v in data.items()}\n", "            lg.info(f\"网格状态已经加载到{file_path}\")\n", "        except Exception as e:\n", "            lg.error(f\"加载网格状态失败: {e}\")\n", "\n", "\n", "class MainController:\n", "    def __init__(self, symbol: str, csv_file: str):\n", "        self.symbol = symbol\n", "        self.today = self.get_us_date()\n", "        self.delayed_timers = {}\n", "        self.PARTIAL_FILL_DELAY = 2\n", "        self.timer_lock = threading.Lock()\n", "\n", "        # 模块初始化\n", "        self.data_feed = DataFeed()\n", "        self.data_feed.get_daily_data(self.symbol, (self.today - timed<PERSON>ta(days=90)).strftime('%Y-%m-%d'), self.today.strftime('%Y-%m-%d'))\n", "        self.strategy = IntegratedGridStrategy(csv_file)\n", "        self.trader = Trader()\n", "\n", "        # 网格状态初始化\n", "        self.strategy.initialize_grid(self.today)\n", "\n", "        # 注册回调事件处理器\n", "        self.trader.set_order_status_handler(self.on_order_status_changed)\n", "\n", "        lg.info(f\"📊 网格策略初始状态:\")\n", "        self._print_grid_status()\n", "\n", "    def start(self):\n", "        lg.info(f\"🚀 启动主交易控制器，标的: {self.symbol}\")\n", "\n", "        def on_tick(tick):\n", "            try:\n", "                if tick['price'] is None or tick['price'] <= 0:\n", "                    lg.error(\"跳过无效行情数据\")\n", "                    return\n", "                current_price = float(tick['price'])\n", "\n", "                lg.info(f\"📈 实时价格: {current_price} ({tick['stage']}) @ {tick['data_time']}\")\n", "\n", "                # 检查是否需要更新网格\n", "                self._check_grid_update()\n", "\n", "                # 获取交易信号\n", "                signals = self.strategy.on_price(current_price)\n", "                if not signals:\n", "                    lg.info(\"🟡 当前无交易信号\")\n", "                    return\n", "\n", "                # 执行所有信号\n", "                for signal in signals:\n", "                    self._execute_signal(signal)\n", "\n", "            except Exception as e:\n", "                lg.error(f\"❌ 行情处理异常: {e}\")\n", "\n", "        # 启动行情监听\n", "        self.data_feed.get_stock_quote(self.symbol, on_tick_callback=on_tick)\n", "\n", "    def get_us_date(self):\n", "        est = pytz.timezone('America/New_York')\n", "        current_date = datetime.now(est).date()\n", "        return current_date\n", "\n", "    def _check_grid_update(self):\n", "        \"\"\"检查是否需要更新网格\"\"\"\n", "        us_date = self.get_us_date()\n", "\n", "        if us_date != self.today:\n", "            lg.info(f\"📅 日期变更，更新网格: {self.today} -> {us_date}\")\n", "            self.today = us_date\n", "            end_date = self.today.strftime('%Y-%m-%d')\n", "            start_date = (self.today - timed<PERSON>ta(days=90)).strftime('%Y-%m-%d')\n", "            # 获取新数据并更新csv\n", "            self.data_feed.get_daily_data(self.symbol, start_date, end_date)\n", "            # 重新加载csv数据\n", "            self.strategy.csv_data = self.strategy._load_csv_data()\n", "            # 更新网格\n", "            self.strategy.update_grid(self.today)\n", "            self._print_grid_status()\n", "\n", "    def _execute_signal(self, signal: Dict[str, Any]):\n", "        \"\"\"执行交易信号\"\"\"\n", "        lg.info(f\"✅ 触发交易信号: Level {signal['level']} - {signal['action']} {signal['qty']}@{signal['price']}\")\n", "\n", "        # 统一的动作映射\n", "        if signal['action'] == TradeAction.SELL:\n", "            side = 'Sell'\n", "            action_desc = \"卖空\"\n", "        elif signal['action'] == TradeAction.BUY:\n", "            side = 'Buy'\n", "            action_desc = \"买回\"\n", "        else:\n", "            lg.error(f\"❌ 未知交易动作: {signal['action']}\")\n", "            return\n", "\n", "        try:\n", "            # 发起交易\n", "            result = self.trader.place_order(\n", "                price=signal['price'],\n", "                quantity=signal['qty'],\n", "                symbol='SQQQ.US',\n", "                side=side\n", "            )\n", "\n", "            if result is not None and result:\n", "                order_id = result\n", "                lg.info(f\"📤 {action_desc}订单已提交 - 订单ID: {order_id}\")\n", "\n", "                # 通知策略信号处理完成\n", "                self.strategy.on_signal_processed(signal, order_id)\n", "            else:\n", "                lg.error(f\"❌ {action_desc}下单失败\")\n", "                # 通知策略下单失败\n", "                self.strategy.on_signal_processed(signal, None)\n", "\n", "        except Exception as e:\n", "            lg.error(f\"❌ 下单异常: {e}\")\n", "            # 通知策略下单失败\n", "            self.strategy.on_signal_processed(signal, None)\n", "\n", "    def _schedule_delayed_update(self, order_id: str):\n", "        \"\"\"调度延迟更新\"\"\"\n", "        with self.timer_lock:\n", "            # 取消之前的定时器\n", "            if order_id in self.delayed_timers:\n", "                self.delayed_timers[order_id].cancel()\n", "\n", "            # 设置新的延迟处理定时器\n", "            self.delayed_timers[order_id] = threading.Timer(\n", "                self.PARTIAL_FILL_DELAY,\n", "                self._handle_partial_fill_timeout,\n", "                [order_id]\n", "            )\n", "            self.delayed_timers[order_id].start()\n", "\n", "    def _cancel_delayed_update(self, order_id: str):\n", "        \"\"\"取消延迟更新\"\"\"\n", "        with self.timer_lock:\n", "            if order_id in self.delayed_timers:\n", "                self.delayed_timers[order_id].cancel()\n", "                del self.delayed_timers[order_id]\n", "\n", "    def _handle_partial_fill_timeout(self, order_id: str):\n", "        \"\"\"处理部分成交延迟超时\"\"\"\n", "        with self.timer_lock:\n", "            if order_id in self.delayed_timers:\n", "                del self.delayed_timers[order_id]\n", "\n", "        try:\n", "            # 检查订单在超时期间是否完全成交\n", "            current_status = self.strategy.check_order_completion(order_id)\n", "            if current_status:\n", "                lg.info(f\"✅ 延迟期间订单完全成交，网格状态已更新\")\n", "                self._print_grid_status()\n", "            else:\n", "                # 任然是部分成交，执行部分成交的网格调整\n", "                lg.info(f\"🔶 部分成交延迟处理完成，网格状态已调整\")\n", "\n", "        except Exception as e:\n", "            lg.error(f\"❌ 部分成交延迟处理异常: {e}\")\n", "\n", "    def on_order_status_changed(self, status_info: Dict[str, Any]):\n", "        \"\"\"处理订单状态变化回报\"\"\"\n", "        order_id = status_info['order_id']\n", "        order_status = status_info['order_status']\n", "\n", "        # 获取订单的成交信息\n", "        executed_qty = status_info.get('qty', 0)\n", "        executed_price = status_info.get('executed_price', 0.0)\n", "        total_qty = status_info.get('quantity', 0)\n", "\n", "        if executed_qty > 0:\n", "            lg.info(f\"   成交进度: {executed_qty}/{total_qty} 股\")\n", "\n", "        # 处理成交状态（部分成交和全部成交）\n", "        if order_status in [OrderStatus.PartialFilled, OrderStatus.Filled]:\n", "            if executed_qty > 0:\n", "                # 构造成交信息\n", "                fill_info = {\n", "                    'order_id': order_id,\n", "                    'action': 'SELL' if status_info.get('side') == 'Sell' else 'BUY',\n", "                    'qty': executed_qty,\n", "                    'price': executed_price\n", "                }\n", "\n", "                lg.info(f\"🎯 成交通知 | 订单ID: {order_id}\")\n", "                lg.info(f\"   {fill_info['action'].upper()} {fill_info['qty']} 股 @ {fill_info['price']}\")\n", "\n", "                # 传递给策略处理，获取是否完全成交的状态\n", "                try:\n", "                    is_fully_filled = self.strategy.on_order_filled(order_id, fill_info)\n", "\n", "                    if is_fully_filled or order_status == OrderStatus.Filled:\n", "                        # 完全成交：立即处理\n", "                        self._cancel_delayed_update(order_id)\n", "                        lg.info(f\"✅ 订单完全成交，策略状态已更新\")\n", "                        self._print_position_status()\n", "                    elif order_status == OrderStatus.PartialFilled:\n", "                        # 部分成交：延迟处理\n", "                        lg.info(f\"🔶 订单部分成交，等待剩余数量成交\")\n", "                        self._schedule_delayed_update(order_id)\n", "\n", "                except Exception as e:\n", "                    lg.error(f\"❌ 策略状态更新异常: {e}\")\n", "\n", "        '''# 处理订单撤销相关状态\n", "        elif order_status in [OrderStatus.Canceled]:\n", "            try:\n", "                self.strategy.on_order_cancelled(order_id, cancelled_type=order_status)\n", "                lg.info(f\"✅ 订单撤销处理完成\")\n", "                self._print_grid_status()\n", "            except Exception as e:\n", "                lg.error(f\"❌ 订单撤销处理异常: {e}\")\n", "\n", "        # 处理订单拒绝/过期状态\n", "        elif order_status in [OrderStatus.Rejected, OrderStatus.Expired]:\n", "            try:\n", "                self.strategy.on_order_cancelled(order_id, cancelled_type=order_status)\n", "                lg.info(f\"✅ 异常订单清理完成\")\n", "            except Exception as e:\n", "                lg.error(f\"❌ 异常订单清理失败: {e}\")'''\n", "\n", "    def _print_grid_status(self):\n", "        \"\"\"打印网格状态\"\"\"\n", "        status = self.strategy.get_grid_status()\n", "        lg.info(f\"基准价: {status['base_price']:.2f}\")\n", "\n", "        # 显示活跃网格层级\n", "        waiting_sell = []\n", "        waiting_buy = []\n", "        pending_orders = []\n", "\n", "        for level, state in status['grid_state'].items():\n", "            if state['status'] == 'waiting_sell':\n", "                waiting_sell.append(f\"L{level}@{state['sell_price']:.2f}\")\n", "            elif state['status'] == 'waiting_buy':\n", "                waiting_buy.append(f\"L{level}@{state['buy_price']:.2f}\")\n", "            elif state['status'] in ['pending_sell', 'pending_buy']:\n", "                pending_orders.append(f\"L{level}({state['status']})\")\n", "\n", "        if waiting_sell:\n", "            lg.info(f\"等待卖空: {', '.join(waiting_sell[:5])}{'...' if len(waiting_sell) > 5 else ''}\")\n", "        if waiting_buy:\n", "            lg.info(f\"等待买回: {', '.join(waiting_buy[:5])}{'...' if len(waiting_buy) > 5 else ''}\")\n", "        if pending_orders:\n", "            lg.info(f\"待成交: {', '.join(pending_orders)}\")\n", "\n", "    def _print_position_status(self):\n", "        \"\"\"打印持仓状态\"\"\"\n", "        status = self.strategy.get_grid_status()\n", "        lg.info(f\"📊 当前持仓: {status['current_position']}\")\n", "\n", "    def get_system_status(self) -> Dict[str, Any]:\n", "        \"\"\"获取系统整体状态\"\"\"\n", "        grid_status = self.strategy.get_grid_status()\n", "        return {\n", "            'symbol': self.symbol,\n", "            'current_date': self.today,\n", "            'grid_status': grid_status\n", "        }\n", "\n", "    def force_sync_position(self):\n", "        \"\"\"强制同步持仓\"\"\"\n", "        try:\n", "            position_df = self.trader.query_position()\n", "            if position_df is not None and not position_df.empty:\n", "                target_position = position_df[position_df['symbol'] == self.symbol]\n", "                if not target_position.empty:\n", "                    real_qty = int(target_position.iloc[0]['quantity'])\n", "                    strategy_qty = self.strategy.current_position\n", "\n", "                    if real_qty != strategy_qty:\n", "                        lg.info(f\"🔄 持仓不同步 - 策略: {strategy_qty}, 实际: {real_qty}\")\n", "                        self.strategy.reset_position(real_qty)\n", "                        lg.info(f\"✅ 持仓已同步至: {real_qty}\")\n", "                    else:\n", "                        lg.info(f\"✅ 持仓已同步: {real_qty}\")\n", "                else:\n", "                    lg.info(f\"📊 {self.symbol} 无持仓\")\n", "                    self.strategy.reset_position(0)\n", "            else:\n", "                lg.info(\"📊 查询持仓为空\")\n", "                self.strategy.reset_position(0)\n", "\n", "            # 同步后清除可能的待成交订单状态\n", "            self.strategy.force_clear_pending_orders()\n", "\n", "        except Exception as e:\n", "            lg.error(f\"❌ 持仓同步异常: {e}\")\n", "\n", "    def stop(self):\n", "        \"\"\"停止交易系统\"\"\"\n", "        lg.info(\"🛑 停止交易系统\")\n", "        try:\n", "            # 显示最终状态\n", "            lg.info(\"📊 最终系统状态:\")\n", "            status = self.get_system_status()\n", "            lg.info(f\"   持仓: {status['grid_status']['current_position']}\")\n", "            lg.info(f\"   待成交订单: {status['grid_status']['pending_orders_count']} 个\")\n", "\n", "            # 停止数据流\n", "            self.data_feed.stop_realtime_quote(self.symbol)\n", "\n", "            # 关闭交易连接\n", "            self.trader.close()\n", "\n", "            lg.info(\"✅ 系统已安全关闭\")\n", "        except Exception as e:\n", "            lg.error(f\"❌ 系统关闭异常: {e}\")\n", "\n", "# ===================== 启动入口 =====================\n", "\n", "if __name__ == \"__main__\":\n", "    SYMBOL = \"US.SQQQ\"\n", "    CSV_FILE = \"US.SQQQ.csv\"\n", "\n", "    bot = MainController(SYMBOL, CSV_FILE)\n", "    try:\n", "        # 启动前先同步一次持仓\n", "        lg.info(\"🔄 启动前持仓同步...\")\n", "        bot.force_sync_position()\n", "\n", "        # 启动系统\n", "        bot.start()\n", "    except KeyboardInterrupt:\n", "        lg.info(\"🔚 用户中断退出\")\n", "    except Exception as e:\n", "        lg.error(f\"❌ 系统异常: {e}\")\n", "    finally:\n", "        bot.stop()"]}, {"cell_type": "code", "execution_count": null, "id": "fab17adb7d4643ea", "metadata": {"ExecuteTime": {"end_time": "2025-08-29T16:39:33.502360Z", "start_time": "2025-08-29T16:39:33.490363Z"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}